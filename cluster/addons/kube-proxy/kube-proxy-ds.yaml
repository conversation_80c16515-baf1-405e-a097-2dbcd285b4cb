# Please keep kube-proxy configuration in-sync with:
# cluster/saltbase/salt/kube-proxy/kube-proxy.manifest

apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    k8s-app: kube-proxy
    addonmanager.kubernetes.io/mode: Reconcile
  name: kube-proxy
  namespace: kube-system
spec:
  selector:
    matchLabels:
      k8s-app: kube-proxy
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 10%
  template:
    metadata:
      labels:
        k8s-app: kube-proxy
    spec:
      priorityClassName: system-node-critical
      hostNetwork: true
      nodeSelector:
        kubernetes.io/os: linux
        node.kubernetes.io/kube-proxy-ds-ready: "true"
      tolerations:
      - operator: "Exists"
        effect: "NoExecute"
      - operator: "Exists"
        effect: "NoSchedule"
      containers:
      - name: kube-proxy
        image: {{pillar['kube_docker_registry']}}/kube-proxy-{{pillar['host_arch']}}:{{pillar['kube-proxy_docker_tag']}}
        resources:
          requests:
            cpu: {{ cpurequest }}
        command:
        - /bin/sh
        - -c
        - kube-proxy {{cluster_cidr}} --oom-score-adj=-998 {{params}} 1>>/var/log/kube-proxy.log 2>&1
        env:
        - name: KUBERNETES_SERVICE_HOST
          value: {{kubernetes_service_host_env_value}}
        {{kube_cache_mutation_detector_env_name}}
          {{kube_cache_mutation_detector_env_value}}
        securityContext:
          privileged: true
        volumeMounts:
        - mountPath: /var/log
          name: varlog
          readOnly: false
        - mountPath: /run/xtables.lock
          name: xtables-lock
          readOnly: false
        - mountPath: /lib/modules
          name: lib-modules
          readOnly: true
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: xtables-lock
        hostPath:
          path: /run/xtables.lock
          type: FileOrCreate
      - name: lib-modules
        hostPath:
          path: /lib/modules
      serviceAccountName: kube-proxy
