apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: gce:podsecuritypolicy:metadata-proxy
  namespace: kube-system
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    kubernetes.io/cluster-service: "true"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: gce:podsecuritypolicy:privileged
subjects:
- kind: ServiceAccount
  name: metadata-proxy
  namespace: kube-system
