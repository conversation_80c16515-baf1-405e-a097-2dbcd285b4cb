action: "22"
apiVersion: events.k8s.io/v1
deprecatedCount: -180456314
deprecatedFirstTimestamp: "2235-03-09T09:44:15Z"
deprecatedLastTimestamp: "2964-12-12T12:04:40Z"
deprecatedSource:
  component: "38"
  host: "39"
eventTime: "2482-03-20T23:11:25.602224Z"
kind: Event
metadata:
  annotations:
    "9": "10"
  creationTimestamp: "2061-09-19T18:13:36Z"
  deletionGracePeriodSeconds: -4955867275792137171
  finalizers:
  - "14"
  generateName: "3"
  generation: 8071137005907523419
  labels:
    "7": "8"
  managedFields:
  - apiVersion: "17"
    fieldsType: "18"
    manager: "16"
    operation: 鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]
    subresource: "19"
  name: "2"
  namespace: "4"
  ownerReferences:
  - apiVersion: "11"
    blockOwnerDeletion: true
    controller: false
    kind: "12"
    name: "13"
    uid: Dz廔ȇ{sŊƏp
  resourceVersion: "11042405498087606203"
  selfLink: "5"
  uid: "7"
note: "36"
reason: "23"
regarding:
  apiVersion: "27"
  fieldPath: "29"
  kind: "24"
  name: "26"
  namespace: "25"
  resourceVersion: "28"
  uid: 鯹)晿<
related:
  apiVersion: "33"
  fieldPath: "35"
  kind: "30"
  name: "32"
  namespace: "31"
  resourceVersion: "34"
  uid: ',c鮽ort昍řČ扷5ƗǸƢ6/ʕ'
reportingController: "20"
reportingInstance: "21"
series:
  count: -1971381490
  lastObservedTime: "2429-02-18T17:57:56.343118Z"
type: "37"
