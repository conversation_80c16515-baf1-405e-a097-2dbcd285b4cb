{"kind": "NetworkPolicy", "apiVersion": "extensions/v1beta1", "metadata": {"name": "2", "generateName": "3", "namespace": "4", "selfLink": "5", "uid": "7", "resourceVersion": "11042405498087606203", "generation": 8071137005907523419, "creationTimestamp": "2061-09-19T18:13:36Z", "deletionGracePeriodSeconds": -4955867275792137171, "labels": {"7": "8"}, "annotations": {"9": "10"}, "ownerReferences": [{"apiVersion": "11", "kind": "12", "name": "13", "uid": "Dz廔ȇ{sŊƏp", "controller": false, "blockOwnerDeletion": true}], "finalizers": ["14"], "managedFields": [{"manager": "16", "operation": "鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]", "apiVersion": "17", "fieldsType": "18", "subresource": "19"}]}, "spec": {"podSelector": {"matchLabels": {"8---jop9641lg.p-g8c2-k-912e5-c-e63-n-3n/E9.8ThjT9s-j41-0-6p-JFHn7y-74.-0MUORQQ.N2.3": "68._bQw.-dG6c-.6--_x.--0wmZk1_8._3s_-_Bq.m_4"}, "matchExpressions": [{"key": "p503---477-49p---o61---4fy--9---7--9-9s-0-u5lj2--10pq-0-7-9-2-0/fP81.-.9Vdx.TB_M-H_5_.t..bG0", "operator": "In", "values": ["D07.a_.y_y_o0_5qN2_---_<PERSON>.N_._a6.9bHjdH.-.5_.I8__n"]}]}, "ingress": [{"ports": [{"protocol": "Ǐ2啗塧ȱ蓿彭聡A3fƻfʣ", "port": 2, "endPort": -420211493}], "from": [{"podSelector": {"matchLabels": {"5__.h-J-M.9_T.q-o7.y-SQ.9A-F-.4--_vLW.jj-.5B.._.5_3-4": "31-4.xXe..03f_--0..L.0qQ6W-.d.20h-OK-_g"}, "matchExpressions": [{"key": "R6S17_.8CnK_O.d-._NwcGnP-w-Sf5_Or.i1_7z.WH-.._Td2-N_Y.v", "operator": "Exists"}]}, "namespaceSelector": {"matchLabels": {"pl6-2-316/NgO-d.iUaC_wYSJfB._.zS-._..3le-Q4-R-083.S5": "U_D__6t-2.-_-8wE._._3.-.83_iq_-y.-25C.A-j..9dfn3Y8d_0_.-y"}, "matchExpressions": [{"key": "f9wk-3--652xh.2a-ik-ak---r0nh-9289---x-p-qpt6-1w-3205c1lxeqyn-5--9d5a3-7bq/4FpF_W-1._-vL_i.-_-a--G-I.-_Y33--.8U.-.5--_zm-.-_RJt2X", "operator": "In", "values": ["g4"]}]}, "ipBlock": {"cidr": "38", "except": ["39"]}}]}], "egress": [{"ports": [{"protocol": "s3!Zɾģ毋", "port": 3, "endPort": -630252364}], "to": [{"podSelector": {"matchLabels": {"P1s-V.9.3": "9..c_uo3a"}, "matchExpressions": [{"key": "1_o_p665O_4Gj._BXt.O-7___-Y_um-_8r--684._-_18_...E.-2oy", "operator": "DoesNotExist"}]}, "namespaceSelector": {"matchLabels": {"5l-59g-qy5--ar-gn58nc2-3--6-o-h-9-15v-5925a-x12a-214-3sc/M.JP_oA_4A.J2s3.XL6_EU--AH-Q.GM7B": "N-_-vv-Q2qz.W..4....-h._.GgT7_7B_D-..-.k4uz"}, "matchExpressions": [{"key": "7u-tie4-7--gm3.38vl-1z---883d-v3j4-7y-p--u/d-4_4--.-_Z4.LA3HVG93_._.I3.__-.0-z_z0sn8", "operator": "DoesNotExist"}]}, "ipBlock": {"cidr": "52", "except": ["53"]}}]}], "policyTypes": ["(dŊiɢz"]}, "status": {}}