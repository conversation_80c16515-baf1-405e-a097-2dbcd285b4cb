apiVersion: authorization.k8s.io/v1beta1
kind: SelfSubjectAccessReview
metadata:
  annotations:
    "9": "10"
  creationTimestamp: "2061-09-19T18:13:36Z"
  deletionGracePeriodSeconds: -4955867275792137171
  finalizers:
  - "14"
  generateName: "3"
  generation: 8071137005907523419
  labels:
    "7": "8"
  managedFields:
  - apiVersion: "17"
    fieldsType: "18"
    manager: "16"
    operation: 鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]
    subresource: "19"
  name: "2"
  namespace: "4"
  ownerReferences:
  - apiVersion: "11"
    blockOwnerDeletion: true
    controller: false
    kind: "12"
    name: "13"
    uid: Dz廔ȇ{sŊƏp
  resourceVersion: "11042405498087606203"
  selfLink: "5"
  uid: "7"
spec:
  nonResourceAttributes:
    path: "27"
    verb: "28"
  resourceAttributes:
    group: "22"
    name: "26"
    namespace: "20"
    resource: "24"
    subresource: "25"
    verb: "21"
    version: "23"
status:
  allowed: true
  evaluationError: "30"
  reason: "29"
