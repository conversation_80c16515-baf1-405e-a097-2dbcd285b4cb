apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  annotations:
    "9": "10"
  creationTimestamp: "2061-09-19T18:13:36Z"
  deletionGracePeriodSeconds: -4955867275792137171
  finalizers:
  - "14"
  generateName: "3"
  generation: 8071137005907523419
  labels:
    "7": "8"
  managedFields:
  - apiVersion: "17"
    fieldsType: "18"
    manager: "16"
    operation: 鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]
    subresource: "19"
  name: "2"
  namespace: "4"
  ownerReferences:
  - apiVersion: "11"
    blockOwnerDeletion: true
    controller: false
    kind: "12"
    name: "13"
    uid: Dz廔ȇ{sŊƏp
  resourceVersion: "11042405498087606203"
  selfLink: "5"
  uid: "7"
spec:
  backend:
    resource:
      apiGroup: "23"
      kind: "24"
      name: "25"
    serviceName: "21"
    servicePort: "22"
  ingressClassName: "20"
  rules:
  - host: "28"
    http:
      paths:
      - backend:
          resource:
            apiGroup: "31"
            kind: "32"
            name: "33"
          serviceName: "30"
          servicePort: 1973774989
        path: "29"
        pathType: )晿<o,c鮽ort昍řČ
  tls:
  - hosts:
    - "26"
    secretName: "27"
status:
  loadBalancer:
    ingress:
    - hostname: "35"
      ip: "34"
      ports:
      - error: "36"
        port: 2114329341
        protocol: Ă凗蓏Ŋ蛊ĉy
