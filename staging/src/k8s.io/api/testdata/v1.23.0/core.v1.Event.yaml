action: "31"
apiVersion: v1
count: -1492226764
eventTime: "2530-04-08T07:06:28.046544Z"
firstTimestamp: "2958-05-23T21:23:39Z"
involvedObject:
  apiVersion: "23"
  fieldPath: "25"
  kind: "20"
  name: "22"
  namespace: "21"
  resourceVersion: "24"
  uid: īqJ枊a8衍`Ĩɘ.蘯
kind: Event
lastTimestamp: "2907-12-28T01:19:18Z"
message: "27"
metadata:
  annotations:
    "9": "10"
  creationTimestamp: "2061-09-19T18:13:36Z"
  deletionGracePeriodSeconds: -4955867275792137171
  finalizers:
  - "14"
  generateName: "3"
  generation: 8071137005907523419
  labels:
    "7": "8"
  managedFields:
  - apiVersion: "17"
    fieldsType: "18"
    manager: "16"
    operation: 鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]
    subresource: "19"
  name: "2"
  namespace: "4"
  ownerReferences:
  - apiVersion: "11"
    blockOwnerDeletion: true
    controller: false
    kind: "12"
    name: "13"
    uid: Dz廔ȇ{sŊƏp
  resourceVersion: "11042405498087606203"
  selfLink: "5"
  uid: "7"
reason: "26"
related:
  apiVersion: "35"
  fieldPath: "37"
  kind: "32"
  name: "34"
  namespace: "33"
  resourceVersion: "36"
  uid: 5ƗǸƢ6/ʕVŚ(ĿȊ甞
reportingComponent: "38"
reportingInstance: "39"
series:
  count: 1266076158
  lastObservedTime: "2951-04-21T20:18:51.456715Z"
source:
  component: "28"
  host: "29"
type: "30"
