{"kind": "ImageReview", "apiVersion": "imagepolicy.k8s.io/v1alpha1", "metadata": {"name": "2", "generateName": "3", "namespace": "4", "selfLink": "5", "uid": "7", "resourceVersion": "11042405498087606203", "generation": 8071137005907523419, "creationTimestamp": "2061-09-19T18:13:36Z", "deletionGracePeriodSeconds": -4955867275792137171, "labels": {"7": "8"}, "annotations": {"9": "10"}, "ownerReferences": [{"apiVersion": "11", "kind": "12", "name": "13", "uid": "Dz廔ȇ{sŊƏp", "controller": false, "blockOwnerDeletion": true}], "finalizers": ["14"], "managedFields": [{"manager": "16", "operation": "鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]", "apiVersion": "17", "fieldsType": "18", "subresource": "19"}]}, "spec": {"containers": [{"image": "20"}], "annotations": {"21": "22"}, "namespace": "23"}, "status": {"allowed": true, "reason": "24", "auditAnnotations": {"25": "26"}}}