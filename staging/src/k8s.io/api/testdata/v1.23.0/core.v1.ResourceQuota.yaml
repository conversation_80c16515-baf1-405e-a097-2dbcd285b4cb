apiVersion: v1
kind: ResourceQuota
metadata:
  annotations:
    "9": "10"
  creationTimestamp: "2061-09-19T18:13:36Z"
  deletionGracePeriodSeconds: -4955867275792137171
  finalizers:
  - "14"
  generateName: "3"
  generation: 8071137005907523419
  labels:
    "7": "8"
  managedFields:
  - apiVersion: "17"
    fieldsType: "18"
    manager: "16"
    operation: 鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]
    subresource: "19"
  name: "2"
  namespace: "4"
  ownerReferences:
  - apiVersion: "11"
    blockOwnerDeletion: true
    controller: false
    kind: "12"
    name: "13"
    uid: Dz廔ȇ{sŊƏp
  resourceVersion: "11042405498087606203"
  selfLink: "5"
  uid: "7"
spec:
  hard:
    '@Hr鯹)晿': "617"
  scopeSelector:
    matchExpressions:
    - operator: 獚敆ȎțêɘĲ斬³;Ơ歿:狞夌碕ʂɭ
      scopeName: ʕVŚ(ĿȊ甞谐颋ǅSǡƏS$+½H牗
      values:
      - "20"
  scopes:
  - Ĩɘ.蘯6ċV夸eɑeʤ脽ěĂ
status:
  hard:
    "": "929"
  used:
    $Iņɖ橙9ȫŚʒUɦOŖ樅: "934"
