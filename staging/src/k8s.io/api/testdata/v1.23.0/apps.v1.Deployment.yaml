apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    "9": "10"
  creationTimestamp: "2061-09-19T18:13:36Z"
  deletionGracePeriodSeconds: -4955867275792137171
  finalizers:
  - "14"
  generateName: "3"
  generation: 8071137005907523419
  labels:
    "7": "8"
  managedFields:
  - apiVersion: "17"
    fieldsType: "18"
    manager: "16"
    operation: 鐊唊飙Ş-U圴÷a/ɔ}摁(湗Ć]
    subresource: "19"
  name: "2"
  namespace: "4"
  ownerReferences:
  - apiVersion: "11"
    blockOwnerDeletion: true
    controller: false
    kind: "12"
    name: "13"
    uid: Dz廔ȇ{sŊƏp
  resourceVersion: "11042405498087606203"
  selfLink: "5"
  uid: "7"
spec:
  minReadySeconds: -463159422
  progressDeadlineSeconds: -487001726
  replicas: 896585016
  revisionHistoryLimit: -855944448
  selector:
    matchExpressions:
    - key: 50-u--25cu87--r7p-w1e67-8pj5t-kl-v0q6b68--nu5oii38fn-8.629b-jd-8c45-0-8--6n--w0--w---196g8d--iv1-5--5ht-a-29--0qso796/3___47._49pIB_o61ISU4--A_.XK_._M99
      operator: Exists
    matchLabels:
      74404d5---g8c2-k-91e.y5-g--58----0683-b-w7ld-6cs06xj-x5yv0wm-k18/M_-Nx.N_6-___._-.-W._AAn---v_-5-_8LXj: 6-4_WE-_JTrcd-2.-__E_Sv__26KX_R_.-.Nth._--S_4DA_-5_-4lQ42M--1
  strategy:
    rollingUpdate:
      maxSurge: 3
      maxUnavailable: 2
    type: 汸<ƋlɋN磋镮ȺPÈɥ偁髕ģƗ鐫
  template:
    metadata:
      annotations:
        "33": "34"
      creationTimestamp: "2063-04-11T19:33:00Z"
      deletionGracePeriodSeconds: -2575298329142810753
      finalizers:
      - "38"
      generateName: "27"
      generation: -8542870036622468681
      labels:
        "31": "32"
      managedFields:
      - apiVersion: "41"
        fieldsType: "42"
        manager: "40"
        operation: 躢
        subresource: "43"
      name: "26"
      namespace: "28"
      ownerReferences:
      - apiVersion: "35"
        blockOwnerDeletion: true
        controller: true
        kind: "36"
        name: "37"
        uid: ƶȤ^}
      resourceVersion: "1736621709629422270"
      selfLink: "29"
      uid: ?Qȫş
    spec:
      activeDeadlineSeconds: 5686960545941743295
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: "425"
                operator: 揤郡ɑ鮽ǍJB膾扉
                values:
                - "426"
              matchFields:
              - key: "427"
                operator: 88 u怞荊ù灹8緔Tj§E蓋C
                values:
                - "428"
            weight: -1759815583
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "421"
                operator: 颪œ]洈愥朘ZǄʤ搤ȃ$|gɳ礬.
                values:
                - "422"
              matchFields:
              - key: "423"
                operator: '%蹶/ʗ'
                values:
                - "424"
        podAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: q1wwv3--f4x4-br5r---r8oh.1nt-23h-4z-21-sap--h--q0h-t2n4s-6-k5-7-a0w8/q.8_00.L
                  operator: Exists
                matchLabels:
                  ? t-9jcz9f-6-4g-z46--f2t-m836.073phjo--8kb6--ut---p8--3-e-3-44---h-q7-p-2djmscp--ac8u23-k/x-_1_-ODgC_1-_8__T3sn-0_.i__a.O2G_-_K-.03.mp.1
                  : 47M7d
              namespaceSelector:
                matchExpressions:
                - key: RT.0zo
                  operator: DoesNotExist
                matchLabels:
                  r4T-I.-..K.-.0__sD.-.-_I-F.PWtO4-7-P41_.-.-AQ._r.Y: w1k8KLu..ly--JM
              namespaces:
              - "449"
              topologyKey: "450"
            weight: -1257588741
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: 0l_.23--_6l.-5_BZk5v3U
                operator: DoesNotExist
              matchLabels:
                t-u-4----q-x3w3dn5-1rhm-5y--z---69o-9-69mxv17r--32b-----4-67t.qk5--f4e4--r1k278l-d-8o1-x-1wl-r/a6Sp_N-S..O-BZ..6-1.b: L_gw_-z6
            namespaceSelector:
              matchExpressions:
              - key: w-_-_ve5.m_2_--Z
                operator: Exists
              matchLabels:
                6Z..11_7pX_.-mLlx...w_t-_.5.40Rw4gD7: 5-x6db-L7.-__-G_2kCpS__3
            namespaces:
            - "435"
            topologyKey: "436"
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: 3.js--a---..6bD_M--c.0Q--2qh.Eb_.__1.-5
                  operator: Exists
                matchLabels:
                  ux_E4-.-PT-_Nx__-F_._n.WaY_o.-0-yE-R5W5_2n...78o: Jj-3.J-.-r_-oPd-.2_Z__.-_U-.60--o._8H__ln_9--Avi.gZdnV
              namespaceSelector:
                matchExpressions:
                - key: Q_mgi.U.-e7z-t0-pQ-.-.g-_Z_-nSL.--4i
                  operator: Exists
                matchLabels:
                  E35H__.B_E: U..u8gwbk
              namespaces:
              - "477"
              topologyKey: "478"
            weight: 339079271
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: 7W-6..4_MU7iLfS-0.9-.-._.1..s._jP6j.u--.K-g
                operator: DoesNotExist
              matchLabels:
                FnV34G._--u.._.105-4_ed-0-i_zZsY_o8t5Vl6_..C: m_dc__G6N-_-0o.0C_gV.9_G-.-z1YH
            namespaceSelector:
              matchExpressions:
              - key: Ky7-_0Vw-Nzfdw.3-._CJ4a1._-_CH--.C.8-S9_-4w
                operator: In
                values:
                - u-_qv4--_.6_N_9X-B.s8.N_rM-k5.C.e.._d--Y-_l-v0-1V-d
              matchLabels:
                p-...Z-O.-.jL_v.-_.4dwFbuvEf55Y22: eF..3m6.._2v89U--8.3N_.n1.--.._-x_4..u2-__3uM77U7.p
            namespaces:
            - "463"
            topologyKey: "464"
      automountServiceAccountToken: false
      containers:
      - args:
        - "255"
        command:
        - "254"
        env:
        - name: "262"
          value: "263"
          valueFrom:
            configMapKeyRef:
              key: "269"
              name: "268"
              optional: false
            fieldRef:
              apiVersion: "264"
              fieldPath: "265"
            resourceFieldRef:
              containerName: "266"
              divisor: "945"
              resource: "267"
            secretKeyRef:
              key: "271"
              name: "270"
              optional: false
        envFrom:
        - configMapRef:
            name: "260"
            optional: false
          prefix: "259"
          secretRef:
            name: "261"
            optional: true
        image: "253"
        imagePullPolicy: e躒訙Ǫʓ)ǂť嗆u8晲T[ir
        lifecycle:
          postStart:
            exec:
              command:
              - "303"
            httpGet:
              host: "306"
              httpHeaders:
              - name: "307"
                value: "308"
              path: "304"
              port: "305"
              scheme: ʒǚ鍰\縑ɀ撑¼蠾8餑噭Dµ
            tcpSocket:
              host: "310"
              port: "309"
          preStop:
            exec:
              command:
              - "311"
            httpGet:
              host: "314"
              httpHeaders:
              - name: "315"
                value: "316"
              path: "312"
              port: "313"
              scheme: ƷƣMț
            tcpSocket:
              host: "318"
              port: "317"
        livenessProbe:
          exec:
            command:
            - "278"
          failureThreshold: -560238386
          grpc:
            port: -1187301925
            service: "285"
          httpGet:
            host: "281"
            httpHeaders:
            - name: "282"
              value: "283"
            path: "279"
            port: "280"
            scheme: Jih亏yƕ丆録²
          initialDelaySeconds: -402384013
          periodSeconds: -617381112
          successThreshold: 1851229369
          tcpSocket:
            host: "284"
            port: 2080874371
          terminationGracePeriodSeconds: 7124276984274024394
          timeoutSeconds: -181601395
        name: "252"
        ports:
        - containerPort: -760292259
          hostIP: "258"
          hostPort: -560717833
          name: "257"
          protocol: w媀瓄&翜舞拉Œɥ颶妧Ö闊 鰔澝qV訆
        readinessProbe:
          exec:
            command:
            - "286"
          failureThreshold: 1956567721
          grpc:
            port: 1443329506
            service: "293"
          httpGet:
            host: "289"
            httpHeaders:
            - name: "290"
              value: "291"
            path: "287"
            port: "288"
            scheme: '"6x$1sȣ±p'
          initialDelaySeconds: 480631652
          periodSeconds: 1167615307
          successThreshold: 455833230
          tcpSocket:
            host: "292"
            port: 1900201288
          terminationGracePeriodSeconds: 666108157153018873
          timeoutSeconds: -1983435813
        resources:
          limits:
            ĩ餠籲磣Óƿ頀"冓鍓贯澔 ƺ蛜6Ɖ飴: "86"
          requests:
            ə娯Ȱ囌{: "853"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            add:
            - Ĕ\ɢX鰨松/Ȁĵ鴁ĩȲǸ|蕎
            drop:
            - 佉賞ǧĒzŔ
          privileged: true
          procMount: b繐汚磉反-n覦灲閈誹
          readOnlyRootFilesystem: false
          runAsGroup: 8906175993302041196
          runAsNonRoot: true
          runAsUser: 3762269034390589700
          seLinuxOptions:
            level: "323"
            role: "321"
            type: "322"
            user: "320"
          seccompProfile:
            localhostProfile: "327"
            type: 蕉ɼ搳ǭ濑箨ʨIk(dŊ
          windowsOptions:
            gmsaCredentialSpec: "325"
            gmsaCredentialSpecName: "324"
            hostProcess: false
            runAsUserName: "326"
        startupProbe:
          exec:
            command:
            - "294"
          failureThreshold: -1835677314
          grpc:
            port: 1473407401
            service: "302"
          httpGet:
            host: "297"
            httpHeaders:
            - name: "298"
              value: "299"
            path: "295"
            port: "296"
            scheme: ɵ
          initialDelaySeconds: 1575106083
          periodSeconds: -1700828941
          successThreshold: 248533396
          tcpSocket:
            host: "301"
            port: "300"
          terminationGracePeriodSeconds: 854912766214576273
          timeoutSeconds: -1995371971
        stdinOnce: true
        terminationMessagePath: "319"
        terminationMessagePolicy: XW疪鑳w妕眵
        tty: true
        volumeDevices:
        - devicePath: "277"
          name: "276"
        volumeMounts:
        - mountPath: "273"
          mountPropagation: 龏´DÒȗÔÂɘɢ鬍
          name: "272"
          readOnly: true
          subPath: "274"
          subPathExpr: "275"
        workingDir: "256"
      dnsConfig:
        nameservers:
        - "491"
        options:
        - name: "493"
          value: "494"
        searches:
        - "492"
      dnsPolicy: hȱɷȰW瀤oɢ嫎
      enableServiceLinks: false
      ephemeralContainers:
      - args:
        - "331"
        command:
        - "330"
        env:
        - name: "338"
          value: "339"
          valueFrom:
            configMapKeyRef:
              key: "345"
              name: "344"
              optional: true
            fieldRef:
              apiVersion: "340"
              fieldPath: "341"
            resourceFieldRef:
              containerName: "342"
              divisor: "334"
              resource: "343"
            secretKeyRef:
              key: "347"
              name: "346"
              optional: true
        envFrom:
        - configMapRef:
            name: "336"
            optional: false
          prefix: "335"
          secretRef:
            name: "337"
            optional: true
        image: "329"
        imagePullPolicy: kƒK07曳wœj堑ūM鈱ɖ'蠨磼
        lifecycle:
          postStart:
            exec:
              command:
              - "379"
            httpGet:
              host: "382"
              httpHeaders:
              - name: "383"
                value: "384"
              path: "380"
              port: "381"
              scheme: đ>*劶?
            tcpSocket:
              host: "385"
              port: -176877925
          preStop:
            exec:
              command:
              - "386"
            httpGet:
              host: "388"
              httpHeaders:
              - name: "389"
                value: "390"
              path: "387"
              port: -783700027
              scheme: '*鑏='
            tcpSocket:
              host: "392"
              port: "391"
        livenessProbe:
          exec:
            command:
            - "354"
          failureThreshold: -1457715462
          grpc:
            port: 1445923603
            service: "362"
          httpGet:
            host: "357"
            httpHeaders:
            - name: "358"
              value: "359"
            path: "355"
            port: "356"
            scheme: 鉂WJ1抉泅ą&疀ȼN翾ȾD虓氙磂t
          initialDelaySeconds: 2040952835
          periodSeconds: -513325570
          successThreshold: 1491794693
          tcpSocket:
            host: "361"
            port: "360"
          terminationGracePeriodSeconds: 5797412715505520759
          timeoutSeconds: -1101457109
        name: "328"
        ports:
        - containerPort: -1844150067
          hostIP: "334"
          hostPort: -370404018
          name: "333"
          protocol: 滞廬耐鷞焬CQm坊柩劄奼[ƕƑĝ®EĨ
        readinessProbe:
          exec:
            command:
            - "363"
          failureThreshold: 731136838
          grpc:
            port: -1459316800
            service: "370"
          httpGet:
            host: "365"
            httpHeaders:
            - name: "366"
              value: "367"
            path: "364"
            port: 534591402
            scheme: ð仁Q橱9ĳ\Ď愝Ű藛b磾sY
          initialDelaySeconds: 343200077
          periodSeconds: -217760519
          successThreshold: 616165315
          tcpSocket:
            host: "369"
            port: "368"
          terminationGracePeriodSeconds: 7306468936162090894
          timeoutSeconds: -1500740922
        resources:
          limits:
            3ǰ廋i乳'ȘUɻ;襕ċ桉桃喕蠲$: "160"
          requests:
            Z漤ŗ坟Ů<y鯶縆ł: "907"
        securityContext:
          allowPrivilegeEscalation: true
          capabilities:
            add:
            - h盌3+Œ
            drop:
            - 两@8Byß讪Ă2讅缔m葰賦迾娙ƴ
          privileged: false
          procMount: ""
          readOnlyRootFilesystem: false
          runAsGroup: -4050404152969473199
          runAsNonRoot: true
          runAsUser: 2527646958598971462
          seLinuxOptions:
            level: "397"
            role: "395"
            type: "396"
            user: "394"
          seccompProfile:
            localhostProfile: "401"
            type: ɴĶ烷Ľthp像-觗裓6Ř
          windowsOptions:
            gmsaCredentialSpec: "399"
            gmsaCredentialSpecName: "398"
            hostProcess: false
            runAsUserName: "400"
        startupProbe:
          exec:
            command:
            - "371"
          failureThreshold: 541943046
          grpc:
            port: -299466656
            service: "378"
          httpGet:
            host: "374"
            httpHeaders:
            - name: "375"
              value: "376"
            path: "372"
            port: "373"
            scheme: 氒ĺʈʫ羶剹ƊF豎穜姰l咑耖p^鏋蛹
          initialDelaySeconds: -656703944
          periodSeconds: -1649234654
          successThreshold: -263708518
          tcpSocket:
            host: "377"
            port: -337985364
          terminationGracePeriodSeconds: 6451878315918197645
          timeoutSeconds: -143604764
        stdinOnce: true
        targetContainerName: "402"
        terminationMessagePath: "393"
        terminationMessagePolicy: '|ǓÓ敆OɈÏ 瞍髃'
        tty: true
        volumeDevices:
        - devicePath: "353"
          name: "352"
        volumeMounts:
        - mountPath: "349"
          mountPropagation: Ò鵌Ē
          name: "348"
          subPath: "350"
          subPathExpr: "351"
        workingDir: "332"
      hostAliases:
      - hostnames:
        - "489"
        ip: "488"
      hostIPC: true
      hostNetwork: true
      hostPID: true
      hostname: "419"
      imagePullSecrets:
      - name: "418"
      initContainers:
      - args:
        - "184"
        command:
        - "183"
        env:
        - name: "191"
          value: "192"
          valueFrom:
            configMapKeyRef:
              key: "198"
              name: "197"
              optional: false
            fieldRef:
              apiVersion: "193"
              fieldPath: "194"
            resourceFieldRef:
              containerName: "195"
              divisor: "573"
              resource: "196"
            secretKeyRef:
              key: "200"
              name: "199"
              optional: false
        envFrom:
        - configMapRef:
            name: "189"
            optional: false
          prefix: "188"
          secretRef:
            name: "190"
            optional: false
        image: "182"
        imagePullPolicy: ĬÇó藢xɮĵȑ6L*Z
        lifecycle:
          postStart:
            exec:
              command:
              - "229"
            httpGet:
              host: "232"
              httpHeaders:
              - name: "233"
                value: "234"
              path: "230"
              port: "231"
              scheme: ȓ蹣ɐǛv+8Ƥ熪军
            tcpSocket:
              host: "235"
              port: 622267234
          preStop:
            exec:
              command:
              - "236"
            httpGet:
              host: "238"
              httpHeaders:
              - name: "239"
                value: "240"
              path: "237"
              port: -1463645123
              scheme: 荙JLĹ]佱¿>犵殇ŕ
            tcpSocket:
              host: "242"
              port: "241"
        livenessProbe:
          exec:
            command:
            - "207"
          failureThreshold: -93157681
          grpc:
            port: -670390306
            service: "213"
          httpGet:
            host: "209"
            httpHeaders:
            - name: "210"
              value: "211"
            path: "208"
            port: -1196874390
            scheme: S晒嶗UÐ_ƮA攤
          initialDelaySeconds: -2036074491
          periodSeconds: 165047920
          successThreshold: -393291312
          tcpSocket:
            host: "212"
            port: -498930176
          terminationGracePeriodSeconds: -4856573944864548413
          timeoutSeconds: -148216266
        name: "181"
        ports:
        - containerPort: 377225334
          hostIP: "187"
          hostPort: 282592353
          name: "186"
          protocol: Ƹ[Ęİ榌U髷裎$MVȟ@7
        readinessProbe:
          exec:
            command:
            - "214"
          failureThreshold: -970312425
          grpc:
            port: -630252364
            service: "220"
          httpGet:
            host: "216"
            httpHeaders:
            - name: "217"
              value: "218"
            path: "215"
            port: -331283026
            scheme: ȉ
          initialDelaySeconds: 391562775
          periodSeconds: -832805508
          successThreshold: -228822833
          tcpSocket:
            host: "219"
            port: 714088955
          terminationGracePeriodSeconds: -5210014804617784724
          timeoutSeconds: -775511009
        resources:
          limits:
            ǚ灄鸫rʤî萨zvt: "829"
          requests:
            悮坮Ȣ幟ļ腻ŬƩȿ0矀Kʝ瘴I\p: "604"
        securityContext:
          allowPrivilegeEscalation: true
          capabilities:
            add:
            - 咡W
            drop:
            - 敄lu|
          privileged: false
          procMount: E埄Ȁ朦 wƯ貾坢'
          readOnlyRootFilesystem: true
          runAsGroup: -4333562938396485230
          runAsNonRoot: false
          runAsUser: -226514069321683925
          seLinuxOptions:
            level: "247"
            role: "245"
            type: "246"
            user: "244"
          seccompProfile:
            localhostProfile: "251"
            type: aŕ翑0展}硐庰%皧V垾现葢ŵ橨鬶l
          windowsOptions:
            gmsaCredentialSpec: "249"
            gmsaCredentialSpecName: "248"
            hostProcess: false
            runAsUserName: "250"
        startupProbe:
          exec:
            command:
            - "221"
          failureThreshold: -1980314709
          grpc:
            port: -1798849477
            service: "228"
          httpGet:
            host: "223"
            httpHeaders:
            - name: "224"
              value: "225"
            path: "222"
            port: -1455098755
            scheme: 眖R#yV'W
          initialDelaySeconds: -1017263912
          periodSeconds: -1252938503
          successThreshold: 893823156
          tcpSocket:
            host: "227"
            port: "226"
          terminationGracePeriodSeconds: 2455602852175027275
          timeoutSeconds: 852780575
        stdin: true
        terminationMessagePath: "243"
        terminationMessagePolicy: 圯W:ĸ輦唊#v铿ʩȂ4ē鐭#嬀
        volumeDevices:
        - devicePath: "206"
          name: "205"
        volumeMounts:
        - mountPath: "202"
          mountPropagation: ƖHV
          name: "201"
          readOnly: true
          subPath: "203"
          subPathExpr: "204"
        workingDir: "185"
      nodeName: "407"
      nodeSelector:
        "403": "404"
      os:
        name: Ê
      overhead:
        隅ǅbİEMǶɼ`|褞: "229"
      preemptionPolicy: n{鳻
      priority: -340583156
      priorityClassName: "490"
      readinessGates:
      - conditionType: țc£PAÎǨȨ栋
      restartPolicy: 5Ų買霎ȃň[>ą S
      runtimeClassName: "495"
      schedulerName: "485"
      securityContext:
        fsGroup: -8312413102936832334
        fsGroupChangePolicy: 洪
        runAsGroup: 5464200670028420111
        runAsNonRoot: false
        runAsUser: -7967112147393038497
        seLinuxOptions:
          level: "411"
          role: "409"
          type: "410"
          user: "408"
        seccompProfile:
          localhostProfile: "417"
          type: 儕lmòɻŶJ詢QǾɁ鍻G
        supplementalGroups:
        - -7991366882837904237
        sysctls:
        - name: "415"
          value: "416"
        windowsOptions:
          gmsaCredentialSpec: "413"
          gmsaCredentialSpecName: "412"
          hostProcess: false
          runAsUserName: "414"
      serviceAccount: "406"
      serviceAccountName: "405"
      setHostnameAsFQDN: false
      shareProcessNamespace: true
      subdomain: "420"
      terminationGracePeriodSeconds: -*****************
      tolerations:
      - key: "486"
        operator: ŭʔb'?舍ȃʥx臥]å摞
        tolerationSeconds: 3053978290188957517
        value: "487"
      topologySpreadConstraints:
      - labelSelector:
          matchExpressions:
          - key: oZvt.LT60v.WxPc---K__-iguFGT._.Y4-0.67hP-lX-_-..b
            operator: NotIn
            values:
            - H1z..j_.r3--T
          matchLabels:
            H_55..--E3_2D-1DW__o_-.k: "7"
        maxSkew: **********
        topologyKey: "496"
        whenUnsatisfiable: ǄɤȶšɞƵõ禲#樹罽濅ʏ 撜粞
      volumes:
      - awsElasticBlockStore:
          fsType: "49"
          partition: *********
          readOnly: true
          volumeID: "48"
        azureDisk:
          cachingMode: '|@?鷅bȻN'
          diskName: "112"
          diskURI: "113"
          fsType: "114"
          kind: 榱*Gưoɘ檲
          readOnly: true
        azureFile:
          readOnly: true
          secretName: "98"
          shareName: "99"
        cephfs:
          monitors:
          - "83"
          path: "84"
          secretFile: "86"
          secretRef:
            name: "87"
          user: "85"
        cinder:
          fsType: "81"
          secretRef:
            name: "82"
          volumeID: "80"
        configMap:
          defaultMode: 1593906314
          items:
          - key: "101"
            mode: 195263908
            path: "102"
          name: "100"
          optional: false
        csi:
          driver: "144"
          fsType: "145"
          nodePublishSecretRef:
            name: "148"
          readOnly: false
          volumeAttributes:
            "146": "147"
        downwardAPI:
          defaultMode: 824682619
          items:
          - fieldRef:
              apiVersion: "91"
              fieldPath: "92"
            mode: 1569992019
            path: "90"
            resourceFieldRef:
              containerName: "93"
              divisor: "660"
              resource: "94"
        emptyDir:
          medium: Xŋ朘瑥A徙ɶɊł/擇ɦĽ胚O醔ɍ厶耈
          sizeLimit: "473"
        ephemeral:
          volumeClaimTemplate:
            metadata:
              annotations:
                "156": "157"
              creationTimestamp: "2003-01-31T21:23:18Z"
              deletionGracePeriodSeconds: 6296624700137074905
              finalizers:
              - "161"
              generateName: "150"
              generation: 6028937828108618026
              labels:
                "154": "155"
              managedFields:
              - apiVersion: "164"
                fieldsType: "165"
                manager: "163"
                operation: ɑ龫`劳&¼傭Ȟ1酃=6}ɡŇƉ立h
                subresource: "166"
              name: "149"
              namespace: "151"
              ownerReferences:
              - apiVersion: "158"
                blockOwnerDeletion: false
                controller: false
                kind: "159"
                name: "160"
                uid: 閝ȝ
              resourceVersion: "11451542506523135343"
              selfLink: "152"
              uid: H巧壚tC十Oɢ
            spec:
              accessModes:
              - '鲡:'
              dataSource:
                apiGroup: "175"
                kind: "176"
                name: "177"
              dataSourceRef:
                apiGroup: "178"
                kind: "179"
                name: "180"
              resources:
                limits:
                  Ŗȫ焗捏ĨFħ籘: "853"
                requests:
                  zɟ踡肒Ao/樝fw[Řż丩ŽoǠ: "918"
              selector:
                matchExpressions:
                - key: m_0_F03_J
                  operator: NotIn
                  values:
                  - 4FpF_W-6
                matchLabels:
                  0-.-yz-0-_p4mz--.I_f6kjsz-7lwY-Y93-6: igm_-._.q6
              storageClassName: "174"
              volumeMode: Z1Ůđ眊ľǎɳ,ǿ飏騀呣
              volumeName: "173"
        fc:
          fsType: "96"
          lun: -1740986684
          readOnly: true
          targetWWNs:
          - "95"
          wwids:
          - "97"
        flexVolume:
          driver: "75"
          fsType: "76"
          options:
            "78": "79"
          readOnly: true
          secretRef:
            name: "77"
        flocker:
          datasetName: "88"
          datasetUUID: "89"
        gcePersistentDisk:
          fsType: "47"
          partition: -1188153605
          pdName: "46"
        gitRepo:
          directory: "52"
          repository: "50"
          revision: "51"
        glusterfs:
          endpoints: "65"
          path: "66"
          readOnly: true
        hostPath:
          path: "45"
          type: ƛƟ)ÙæNǚ錯ƶRquA?瞲Ť倱<
        iscsi:
          chapAuthDiscovery: true
          fsType: "61"
          initiatorName: "64"
          iqn: "59"
          iscsiInterface: "60"
          lun: 994527057
          portals:
          - "62"
          secretRef:
            name: "63"
          targetPortal: "58"
        name: "44"
        nfs:
          path: "57"
          readOnly: true
          server: "56"
        persistentVolumeClaim:
          claimName: "67"
          readOnly: true
        photonPersistentDisk:
          fsType: "116"
          pdID: "115"
        portworxVolume:
          fsType: "131"
          readOnly: true
          volumeID: "130"
        projected:
          defaultMode: -1334904807
          sources:
          - configMap:
              items:
              - key: "126"
                mode: 2063799569
                path: "127"
              name: "125"
              optional: false
            downwardAPI:
              items:
              - fieldRef:
                  apiVersion: "121"
                  fieldPath: "122"
                mode: *********
                path: "120"
                resourceFieldRef:
                  containerName: "123"
                  divisor: "106"
                  resource: "124"
            secret:
              items:
              - key: "118"
                mode: -*********
                path: "119"
              name: "117"
              optional: true
            serviceAccountToken:
              audience: "128"
              expirationSeconds: 8357931971650847566
              path: "129"
        quobyte:
          group: "110"
          registry: "107"
          tenant: "111"
          user: "109"
          volume: "108"
        rbd:
          fsType: "70"
          image: "69"
          keyring: "73"
          monitors:
          - "68"
          pool: "71"
          secretRef:
            name: "74"
          user: "72"
        scaleIO:
          fsType: "139"
          gateway: "132"
          protectionDomain: "135"
          secretRef:
            name: "134"
          storageMode: "137"
          storagePool: "136"
          system: "133"
          volumeName: "138"
        secret:
          defaultMode: *********
          items:
          - key: "54"
            mode: -*********
            path: "55"
          optional: true
          secretName: "53"
        storageos:
          fsType: "142"
          secretRef:
            name: "143"
          volumeName: "140"
          volumeNamespace: "141"
        vsphereVolume:
          fsType: "104"
          storagePolicyID: "106"
          storagePolicyName: "105"
          volumePath: "103"
status:
  availableReplicas: 1731921624
  collisionCount: 275578828
  conditions:
  - lastTransitionTime: "2140-08-23T12:38:52Z"
    lastUpdateTime: "2294-08-28T16:58:10Z"
    message: "504"
    reason: "503"
    status: 桼劑躮ǿȦU锭ǭ舒
    type: ¦褅桃|薝Țµʍ^鼑:$Ǿ觇ƒ
  observedGeneration: -430213889424572931
  readyReplicas: -1612961101
  replicas: -1728725476
  unavailableReplicas: 826023875
  updatedReplicas: -36544080
