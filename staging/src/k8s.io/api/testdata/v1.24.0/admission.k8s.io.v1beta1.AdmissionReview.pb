k8s 
+
admission.k8s.io/v1beta1AdmissionReview�
�
uidValue%

groupValueversionValue	kindValue)

groupValueversionValue
resourceValue"subResourceValue*	nameValue2namespaceValue:operationValueB@

usernameValueuidValuegroupsValue"
extraKey

extraValueJd
b{"apiVersion":"example.com/v1","kind":"CustomType","spec":{"replicas":1},"status":{"available":1}}Rd
b{"apiVersion":"example.com/v1","kind":"CustomType","spec":{"replicas":1},"status":{"available":1}}Xbd
b{"apiVersion":"example.com/v1","kind":"CustomType","spec":{"replicas":1},"status":{"available":1}}j%

groupValueversionValue	kindValuer)

groupValueversionValue
resourceValuezrequestSubResourceValue�
uidValue�
6

selfLinkValueresourceVersionValue
continueValue statusValuemessageValue"reasonValue*W
	nameValue
groupValue	kindValue"'
reasonValuemessageValue
fieldValue(2uidValue0"*patchTypeValue2,
auditAnnotationsKeyauditAnnotationsValue:
warningsValue " 