k8s 

v1
LimitRange�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValuex
v
	typeValue
maxKey
0
minKey
0"

defaultKey
0*
defaultRequestKey
02
maxLimitRequestRatioKey
0 " 