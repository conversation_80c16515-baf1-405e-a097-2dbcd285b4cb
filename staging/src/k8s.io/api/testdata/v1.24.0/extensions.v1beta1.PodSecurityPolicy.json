{"kind": "PodSecurityPolicy", "apiVersion": "extensions/v1beta1", "metadata": {"name": "nameValue", "generateName": "generateNameValue", "namespace": "namespaceValue", "selfLink": "selfLinkValue", "uid": "uidValue", "resourceVersion": "resourceVersionValue", "generation": 7, "creationTimestamp": "2008-01-01T01:01:01Z", "deletionTimestamp": "2009-01-01T01:01:01Z", "deletionGracePeriodSeconds": 10, "labels": {"labelsKey": "labelsValue"}, "annotations": {"annotationsKey": "annotationsValue"}, "ownerReferences": [{"apiVersion": "apiVersionValue", "kind": "kindValue", "name": "nameValue", "uid": "uidValue", "controller": true, "blockOwnerDeletion": true}], "finalizers": ["finalizersV<PERSON>ue"], "managedFields": [{"manager": "<PERSON><PERSON><PERSON><PERSON>", "operation": "operationValue", "apiVersion": "apiVersionValue", "time": "2004-01-01T01:01:01Z", "fieldsType": "fieldsTypeValue", "fieldsV1": {}, "subresource": "subresourceValue"}]}, "spec": {"privileged": true, "defaultAddCapabilities": ["defaultAddCapabilitiesValue"], "requiredDropCapabilities": ["requiredDropCapabilitiesValue"], "allowedCapabilities": ["allowedCapabilitiesValue"], "volumes": ["volumesValue"], "hostNetwork": true, "hostPorts": [{"min": 1, "max": 2}], "hostPID": true, "hostIPC": true, "seLinux": {"rule": "ruleValue", "seLinuxOptions": {"user": "userValue", "role": "roleValue", "type": "typeValue", "level": "levelValue"}}, "runAsUser": {"rule": "ruleValue", "ranges": [{"min": 1, "max": 2}]}, "runAsGroup": {"rule": "ruleValue", "ranges": [{"min": 1, "max": 2}]}, "supplementalGroups": {"rule": "ruleValue", "ranges": [{"min": 1, "max": 2}]}, "fsGroup": {"rule": "ruleValue", "ranges": [{"min": 1, "max": 2}]}, "readOnlyRootFilesystem": true, "defaultAllowPrivilegeEscalation": true, "allowPrivilegeEscalation": true, "allowedHostPaths": [{"pathPrefix": "pathPrefixValue", "readOnly": true}], "allowedFlexVolumes": [{"driver": "driver<PERSON><PERSON><PERSON>"}], "allowedCSIDrivers": [{"name": "nameValue"}], "allowedUnsafeSysctls": ["allowedUnsafeSysctlsValue"], "forbiddenSysctls": ["forbiddenSysctlsValue"], "allowedProcMountTypes": ["allowedProcMountTypesValue"], "runtimeClass": {"allowedRuntimeClassNames": ["allowedRuntimeClassNamesValue"], "defaultRuntimeClassName": "defaultRuntimeClassNameValue"}}}