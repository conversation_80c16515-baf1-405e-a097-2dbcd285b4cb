{"kind": "RoleBinding", "apiVersion": "rbac.authorization.k8s.io/v1alpha1", "metadata": {"name": "nameValue", "generateName": "generateNameValue", "namespace": "namespaceValue", "selfLink": "selfLinkValue", "uid": "uidValue", "resourceVersion": "resourceVersionValue", "generation": 7, "creationTimestamp": "2008-01-01T01:01:01Z", "deletionTimestamp": "2009-01-01T01:01:01Z", "deletionGracePeriodSeconds": 10, "labels": {"labelsKey": "labelsValue"}, "annotations": {"annotationsKey": "annotationsValue"}, "ownerReferences": [{"apiVersion": "apiVersionValue", "kind": "kindValue", "name": "nameValue", "uid": "uidValue", "controller": true, "blockOwnerDeletion": true}], "finalizers": ["finalizersV<PERSON>ue"], "managedFields": [{"manager": "<PERSON><PERSON><PERSON><PERSON>", "operation": "operationValue", "apiVersion": "apiVersionValue", "time": "2004-01-01T01:01:01Z", "fieldsType": "fieldsTypeValue", "fieldsV1": {}, "subresource": "subresourceValue"}]}, "subjects": [{"kind": "kindValue", "apiVersion": "apiVersionValue", "name": "nameValue", "namespace": "namespaceValue"}], "roleRef": {"apiGroup": "apiGroupValue", "kind": "kindValue", "name": "nameValue"}}