k8s 
3
%flowcontrol.apiserver.k8s.io/v1alpha1
FlowSchema�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue�

	nameValue
	typeValue"�
B
	kindValue
	nameValue
	nameValue"
namespaceValue	nameValue?

verbsValueapiGroupsValueresourcesValue *namespacesValue"

verbsValue2nonResourceURLsValue?
=
	typeValuestatusValue���� "reasonValue*messageValue " 