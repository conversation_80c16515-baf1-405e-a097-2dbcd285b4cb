k8s 

v1PersistentVolumeClaim�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue�
accessModesValue&

	limitsKey
0
requestsKey
0volumeNameValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue*storageClassNameValue2volumeModeValue:%

apiGroupValue	kindValue	nameValueB%

apiGroupValue	kindValue	nameValue�

phaseValueaccessModesValue
capacityKey
0"G
	typeValuestatusValue���� "���� *reasonValue2messageValue*
allocatedResourcesKey
02resizeStatusValue " 