k8s 
+
rbac.authorization.k8s.io/v1ClusterRole�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValueV

verbsValueapiGroupsValueresourcesValue"resourceNamesValue*nonResourceURLsValueN
L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue " 