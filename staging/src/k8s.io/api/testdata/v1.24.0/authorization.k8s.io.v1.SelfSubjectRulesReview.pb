k8s 
1
authorization.k8s.io/v1SelfSubjectRulesReview�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue
namespaceValue~
@

verbsValueapiGroupsValueresourcesValue"resourceNamesValue"

verbsValuenonResourceURLsValue"evaluationErrorValue " 