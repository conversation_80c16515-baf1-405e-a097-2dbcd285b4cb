apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  annotations:
    annotationsKey: annotationsValue
  creationTimestamp: "2008-01-01T01:01:01Z"
  deletionGracePeriodSeconds: 10
  deletionTimestamp: "2009-01-01T01:01:01Z"
  finalizers:
  - finalizersValue
  generateName: generateNameValue
  generation: 7
  labels:
    labelsKey: labelsValue
  managedFields:
  - apiVersion: apiVersionValue
    fieldsType: fieldsTypeValue
    fieldsV1: {}
    manager: managerValue
    operation: operationValue
    subresource: subresourceValue
    time: "2004-01-01T01:01:01Z"
  name: nameValue
  namespace: namespaceValue
  ownerReferences:
  - apiVersion: apiVersionValue
    blockOwnerDeletion: true
    controller: true
    kind: kindValue
    name: nameValue
    uid: uidValue
  resourceVersion: resourceVersionValue
  selfLink: selfLinkValue
  uid: uidValue
spec:
  egress:
  - ports:
    - endPort: 3
      port: portValue
      protocol: protocolValue
    to:
    - ipBlock:
        cidr: cidrValue
        except:
        - exceptValue
      namespaceSelector:
        matchExpressions:
        - key: keyValue
          operator: operatorValue
          values:
          - valuesValue
        matchLabels:
          matchLabelsKey: matchLabelsValue
      podSelector:
        matchExpressions:
        - key: keyValue
          operator: operatorValue
          values:
          - valuesValue
        matchLabels:
          matchLabelsKey: matchLabelsValue
  ingress:
  - from:
    - ipBlock:
        cidr: cidrValue
        except:
        - exceptValue
      namespaceSelector:
        matchExpressions:
        - key: keyValue
          operator: operatorValue
          values:
          - valuesValue
        matchLabels:
          matchLabelsKey: matchLabelsValue
      podSelector:
        matchExpressions:
        - key: keyValue
          operator: operatorValue
          values:
          - valuesValue
        matchLabels:
          matchLabelsKey: matchLabelsValue
    ports:
    - endPort: 3
      port: portValue
      protocol: protocolValue
  podSelector:
    matchExpressions:
    - key: keyValue
      operator: operatorValue
      values:
      - valuesValue
    matchLabels:
      matchLabelsKey: matchLabelsValue
  policyTypes:
  - policyTypesValue
status:
  conditions:
  - lastTransitionTime: "2004-01-01T01:01:01Z"
    message: messageValue
    observedGeneration: 3
    reason: reasonValue
    status: statusValue
    type: typeValue
