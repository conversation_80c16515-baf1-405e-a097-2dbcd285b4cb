apiVersion: storage.k8s.io/v1
kind: VolumeAttachment
metadata:
  annotations:
    annotationsKey: annotationsValue
  creationTimestamp: "2008-01-01T01:01:01Z"
  deletionGracePeriodSeconds: 10
  deletionTimestamp: "2009-01-01T01:01:01Z"
  finalizers:
  - finalizersValue
  generateName: generateNameValue
  generation: 7
  labels:
    labelsKey: labelsValue
  managedFields:
  - apiVersion: apiVersionValue
    fieldsType: fieldsTypeValue
    fieldsV1: {}
    manager: managerValue
    operation: operationValue
    subresource: subresourceValue
    time: "2004-01-01T01:01:01Z"
  name: nameValue
  namespace: namespaceValue
  ownerReferences:
  - apiVersion: apiVersionValue
    blockOwnerDeletion: true
    controller: true
    kind: kindValue
    name: nameValue
    uid: uidValue
  resourceVersion: resourceVersionValue
  selfLink: selfLinkValue
  uid: uidValue
spec:
  attacher: attacherValue
  nodeName: nodeNameValue
  source:
    inlineVolumeSpec:
      accessModes:
      - accessModesValue
      awsElasticBlockStore:
        fsType: fsTypeValue
        partition: 3
        readOnly: true
        volumeID: volumeIDValue
      azureDisk:
        cachingMode: cachingModeValue
        diskName: diskNameValue
        diskURI: diskURIValue
        fsType: fsTypeValue
        kind: kindValue
        readOnly: true
      azureFile:
        readOnly: true
        secretName: secretNameValue
        secretNamespace: secretNamespaceValue
        shareName: shareNameValue
      capacity:
        capacityKey: "0"
      cephfs:
        monitors:
        - monitorsValue
        path: pathValue
        readOnly: true
        secretFile: secretFileValue
        secretRef:
          name: nameValue
          namespace: namespaceValue
        user: userValue
      cinder:
        fsType: fsTypeValue
        readOnly: true
        secretRef:
          name: nameValue
          namespace: namespaceValue
        volumeID: volumeIDValue
      claimRef:
        apiVersion: apiVersionValue
        fieldPath: fieldPathValue
        kind: kindValue
        name: nameValue
        namespace: namespaceValue
        resourceVersion: resourceVersionValue
        uid: uidValue
      csi:
        controllerExpandSecretRef:
          name: nameValue
          namespace: namespaceValue
        controllerPublishSecretRef:
          name: nameValue
          namespace: namespaceValue
        driver: driverValue
        fsType: fsTypeValue
        nodeExpandSecretRef:
          name: nameValue
          namespace: namespaceValue
        nodePublishSecretRef:
          name: nameValue
          namespace: namespaceValue
        nodeStageSecretRef:
          name: nameValue
          namespace: namespaceValue
        readOnly: true
        volumeAttributes:
          volumeAttributesKey: volumeAttributesValue
        volumeHandle: volumeHandleValue
      fc:
        fsType: fsTypeValue
        lun: 2
        readOnly: true
        targetWWNs:
        - targetWWNsValue
        wwids:
        - wwidsValue
      flexVolume:
        driver: driverValue
        fsType: fsTypeValue
        options:
          optionsKey: optionsValue
        readOnly: true
        secretRef:
          name: nameValue
          namespace: namespaceValue
      flocker:
        datasetName: datasetNameValue
        datasetUUID: datasetUUIDValue
      gcePersistentDisk:
        fsType: fsTypeValue
        partition: 3
        pdName: pdNameValue
        readOnly: true
      glusterfs:
        endpoints: endpointsValue
        endpointsNamespace: endpointsNamespaceValue
        path: pathValue
        readOnly: true
      hostPath:
        path: pathValue
        type: typeValue
      iscsi:
        chapAuthDiscovery: true
        chapAuthSession: true
        fsType: fsTypeValue
        initiatorName: initiatorNameValue
        iqn: iqnValue
        iscsiInterface: iscsiInterfaceValue
        lun: 3
        portals:
        - portalsValue
        readOnly: true
        secretRef:
          name: nameValue
          namespace: namespaceValue
        targetPortal: targetPortalValue
      local:
        fsType: fsTypeValue
        path: pathValue
      mountOptions:
      - mountOptionsValue
      nfs:
        path: pathValue
        readOnly: true
        server: serverValue
      nodeAffinity:
        required:
          nodeSelectorTerms:
          - matchExpressions:
            - key: keyValue
              operator: operatorValue
              values:
              - valuesValue
            matchFields:
            - key: keyValue
              operator: operatorValue
              values:
              - valuesValue
      persistentVolumeReclaimPolicy: persistentVolumeReclaimPolicyValue
      photonPersistentDisk:
        fsType: fsTypeValue
        pdID: pdIDValue
      portworxVolume:
        fsType: fsTypeValue
        readOnly: true
        volumeID: volumeIDValue
      quobyte:
        group: groupValue
        readOnly: true
        registry: registryValue
        tenant: tenantValue
        user: userValue
        volume: volumeValue
      rbd:
        fsType: fsTypeValue
        image: imageValue
        keyring: keyringValue
        monitors:
        - monitorsValue
        pool: poolValue
        readOnly: true
        secretRef:
          name: nameValue
          namespace: namespaceValue
        user: userValue
      scaleIO:
        fsType: fsTypeValue
        gateway: gatewayValue
        protectionDomain: protectionDomainValue
        readOnly: true
        secretRef:
          name: nameValue
          namespace: namespaceValue
        sslEnabled: true
        storageMode: storageModeValue
        storagePool: storagePoolValue
        system: systemValue
        volumeName: volumeNameValue
      storageClassName: storageClassNameValue
      storageos:
        fsType: fsTypeValue
        readOnly: true
        secretRef:
          apiVersion: apiVersionValue
          fieldPath: fieldPathValue
          kind: kindValue
          name: nameValue
          namespace: namespaceValue
          resourceVersion: resourceVersionValue
          uid: uidValue
        volumeName: volumeNameValue
        volumeNamespace: volumeNamespaceValue
      volumeMode: volumeModeValue
      vsphereVolume:
        fsType: fsTypeValue
        storagePolicyID: storagePolicyIDValue
        storagePolicyName: storagePolicyNameValue
        volumePath: volumePathValue
    persistentVolumeName: persistentVolumeNameValue
status:
  attachError:
    message: messageValue
    time: "2001-01-01T01:01:01Z"
  attached: true
  attachmentMetadata:
    attachmentMetadataKey: attachmentMetadataValue
  detachError:
    message: messageValue
    time: "2001-01-01T01:01:01Z"
