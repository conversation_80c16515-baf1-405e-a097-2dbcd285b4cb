k8s 
$
discovery.k8s.io/v1
EndpointSlice�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue�
addressesValue
hostnameValue"g
	kindValuenamespaceValue	nameValue"uidValue*apiVersionValue2resourceVersionValue:fieldPathValue*0
deprecatedTopologyKeydeprecatedTopologyValue2
nodeNameValue:	zoneValueB

	nameValue.
	nameValue
protocolValue"appProtocolValue"addressTypeValue " 