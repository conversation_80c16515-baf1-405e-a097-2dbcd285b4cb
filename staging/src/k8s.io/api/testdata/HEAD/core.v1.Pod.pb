k8s 
	
v1Pod�P
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue�E
�
	nameValue�

	pathValue	typeValue
mediumValue
0
pdNameValuefsTypeValue " 

volumeIDValuefsTypeValue *0
repositoryValue
revisionValuedirectoryValue2.
secretNameValue
keyValue	pathValue :
serverValue	pathValueBv
targetPortalValueiqnValue"iscsiInterfaceValue*fsTypeValue0:portalsValue@R
	nameValueXbinitiatorNameValueJ
endpointsValue	pathValueR
claimNameValueZ[

monitorsValue
imageValuefsTypeValue"	poolValue*	userValue2keyringValue:
	nameValue@bE
driverValuefsTypeValue
	nameValue *

optionsKeyoptionsValuej+

volumeIDValuefsTypeValue"
	nameValuerE

monitorsValue	pathValue	userValue"secretFileValue*
	nameValue0z$
datasetNameValuedatasetUUIDValue�^
Z
	pathValue!
apiVersionValuefieldPathValue(
containerNameValue
resourceValue
0 �.
targetWWNsValuefsTypeValue *
wwidsValue�#
secretNameValueshareNameValue�*

	nameValue
keyValue	pathValue �L
volumePathValuefsTypeValuestoragePolicyNameValue"storagePolicyIDValue�B

registryValuevolumeValue"	userValue*
groupValue2tenantValue�I

diskNameValuediskURIValuecachingModeValue"fsTypeValue(2	kindValue�
	pdIDValuefsTypeValue�

volumeIDValuefsTypeValue��
gatewayValuesystemValue
	nameValue *protectionDomainValue2storagePoolValue:storageModeValueBvolumeNameValueJfsTypeValueP��
�
(

	nameValue
keyValue	pathValue \
Z
	pathValue!
apiVersionValuefieldPathValue(
containerNameValue
resourceValue
0 (

	nameValue
keyValue	pathValue "

audienceValue	pathValue�C
volumeNameValuevolumeNamespaceValuefsTypeValue *
	nameValue�W
driverValuefsTypeValue",
volumeAttributesKeyvolumeAttributesValue*
	nameValue��
�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue�
accessModesValue&

	limitsKey
0
requestsKey
0volumeNameValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue*storageClassNameValue2volumeModeValue:%

apiGroupValue	kindValue	nameValueB%

apiGroupValue	kindValue	nameValue�
	nameValue
imageValuecommandValue"	argsValue*workingDirValue2+
	nameValue"
protocolValue*hostIPValue:�
	nameValue
valueValue�
!
apiVersionValuefieldPathValue(
containerNameValue
resourceValue
0

	nameValuekeyValue"

	nameValuekeyValueB&

	limitsKey
0
requestsKey
0JT
	nameValuemountPathValue"subPathValue*mountPropagationValue2subPathExprValueR�
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08Z�
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08b�
}

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue}

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValuejterminationMessagePathValuerimagePullPolicyValuez�

addValue	dropValue-
	userValue	roleValue	typeValue"
levelValue (08@JprocMountValueRL
gmsaCredentialSpecNameValuegmsaCredentialSpecValuerunAsUserNameValue Z"
	typeValuelocalhostProfileValue����/
prefixValue

	nameValue

	nameValue�terminationMessagePolicyValue�
	nameValuedevicePathValue��
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08restartPolicyValue (2dnsPolicyValue:$
nodeSelectorKeynodeSelectorValueBserviceAccountNameValueJserviceAccountValueR
nodeNameValueX`hr�
-
	userValue	roleValue	typeValue"
levelValue (0:
	nameValue
valueValueBL
gmsaCredentialSpecNameValuegmsaCredentialSpecValuerunAsUserNameValue JfsGroupChangePolicyValueR"
	typeValuelocalhostProfileValuez
	nameValue�
hostnameValue�subdomainValue��
�
R
P
&
keyValue
operatorValuevaluesValue&
keyValue
operatorValuevaluesValueTP
&
keyValue
operatorValuevaluesValue&
keyValue
operatorValuevaluesValue�
�
L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValuenamespacesValuetopologyKeyValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue��
L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValuenamespacesValuetopologyKeyValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue�
�
L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValuenamespacesValuetopologyKeyValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue��
L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValuenamespacesValuetopologyKeyValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue�schedulerNameValue��
	nameValue
imageValuecommandValue"	argsValue*workingDirValue2+
	nameValue"
protocolValue*hostIPValue:�
	nameValue
valueValue�
!
apiVersionValuefieldPathValue(
containerNameValue
resourceValue
0

	nameValuekeyValue"

	nameValuekeyValueB&

	limitsKey
0
requestsKey
0JT
	nameValuemountPathValue"subPathValue*mountPropagationValue2subPathExprValueR�
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08Z�
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08b�
}

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue}

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValuejterminationMessagePathValuerimagePullPolicyValuez�

addValue	dropValue-
	userValue	roleValue	typeValue"
levelValue (08@JprocMountValueRL
gmsaCredentialSpecNameValuegmsaCredentialSpecValuerunAsUserNameValue Z"
	typeValuelocalhostProfileValue����/
prefixValue

	nameValue

	nameValue�terminationMessagePolicyValue�
	nameValuedevicePathValue��
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08��4
keyValue
operatorValue
valueValue"effectValue(�
ipValuehostnamesValue�priorityClassNameValue��:
nameserversValue
searchesValue
	nameValue
valueValue��
conditionTypeValue�runtimeClassNameValue��preemptionPolicyValue�
overheadKey
0��topologyKeyValuewhenUnsatisfiableValue"L
"
matchLabelsKeymatchLabelsValue&
keyValue
operatorValuevaluesValue(2nodeAffinityPolicyValue:nodeTaintsPolicyValueBmatchLabelKeysValue��
�
	nameValue
imageValuecommandValue"	argsValue*workingDirValue2+
	nameValue"
protocolValue*hostIPValue:�
	nameValue
valueValue�
!
apiVersionValuefieldPathValue(
containerNameValue
resourceValue
0

	nameValuekeyValue"

	nameValuekeyValueB&

	limitsKey
0
requestsKey
0JT
	nameValuemountPathValue"subPathValue*mountPropagationValue2subPathExprValueR�
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08Z�
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08b�
}

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue}

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValuejterminationMessagePathValuerimagePullPolicyValuez�

addValue	dropValue-
	userValue	roleValue	typeValue"
levelValue (08@JprocMountValueRL
gmsaCredentialSpecNameValuegmsaCredentialSpecValuerunAsUserNameValue Z"
	typeValuelocalhostProfileValue����/
prefixValue

	nameValue

	nameValue�terminationMessagePolicyValue�
	nameValuedevicePathValue��
�

commandValueM
	pathValue 	portValue	hostValue"schemeValue*
	nameValue
valueValue
 	portValue	hostValue"serviceValue (08targetContainerNameValue��
	nameValue��

phaseValueG
	typeValuestatusValue���� "���� *reasonValue2messageValuemessageValue"reasonValue*hostIPValue2
podIPValue:ͺ� B�
	nameValuep

reasonValuemessageValue

ͭ�� EreasonValue"messageValue*��׎ 2��ܝ :containerIDValuep

reasonValuemessageValue

ͭ�� EreasonValue"messageValue*��׎ 2��ܝ :containerIDValue (2
imageValue:imageIDValueBcontainerIDValueHJ
qosClassValueR�
	nameValuep

reasonValuemessageValue

ͭ�� EreasonValue"messageValue*��׎ 2��ܝ :containerIDValuep

reasonValuemessageValue

ͭ�� EreasonValue"messageValue*��׎ 2��ܝ :containerIDValue (2
imageValue:imageIDValueBcontainerIDValueHZnominatedNodeNameValueb	
ipValuej�
	nameValuep

reasonValuemessageValue

ͭ�� EreasonValue"messageValue*��׎ 2��ܝ :containerIDValuep

reasonValuemessageValue

ͭ�� EreasonValue"messageValue*��׎ 2��ܝ :containerIDValue (2
imageValue:imageIDValueBcontainerIDValueH " 