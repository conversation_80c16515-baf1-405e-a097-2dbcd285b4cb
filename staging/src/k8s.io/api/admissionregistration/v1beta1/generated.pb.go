/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: k8s.io/kubernetes/vendor/k8s.io/api/admissionregistration/v1beta1/generated.proto

package v1beta1

import (
	fmt "fmt"

	io "io"

	proto "github.com/gogo/protobuf/proto"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func (m *MutatingWebhook) Reset()      { *m = MutatingWebhook{} }
func (*MutatingWebhook) ProtoMessage() {}
func (*MutatingWebhook) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{0}
}
func (m *MutatingWebhook) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MutatingWebhook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *MutatingWebhook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MutatingWebhook.Merge(m, src)
}
func (m *MutatingWebhook) XXX_Size() int {
	return m.Size()
}
func (m *MutatingWebhook) XXX_DiscardUnknown() {
	xxx_messageInfo_MutatingWebhook.DiscardUnknown(m)
}

var xxx_messageInfo_MutatingWebhook proto.InternalMessageInfo

func (m *MutatingWebhookConfiguration) Reset()      { *m = MutatingWebhookConfiguration{} }
func (*MutatingWebhookConfiguration) ProtoMessage() {}
func (*MutatingWebhookConfiguration) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{1}
}
func (m *MutatingWebhookConfiguration) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MutatingWebhookConfiguration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *MutatingWebhookConfiguration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MutatingWebhookConfiguration.Merge(m, src)
}
func (m *MutatingWebhookConfiguration) XXX_Size() int {
	return m.Size()
}
func (m *MutatingWebhookConfiguration) XXX_DiscardUnknown() {
	xxx_messageInfo_MutatingWebhookConfiguration.DiscardUnknown(m)
}

var xxx_messageInfo_MutatingWebhookConfiguration proto.InternalMessageInfo

func (m *MutatingWebhookConfigurationList) Reset()      { *m = MutatingWebhookConfigurationList{} }
func (*MutatingWebhookConfigurationList) ProtoMessage() {}
func (*MutatingWebhookConfigurationList) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{2}
}
func (m *MutatingWebhookConfigurationList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MutatingWebhookConfigurationList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *MutatingWebhookConfigurationList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MutatingWebhookConfigurationList.Merge(m, src)
}
func (m *MutatingWebhookConfigurationList) XXX_Size() int {
	return m.Size()
}
func (m *MutatingWebhookConfigurationList) XXX_DiscardUnknown() {
	xxx_messageInfo_MutatingWebhookConfigurationList.DiscardUnknown(m)
}

var xxx_messageInfo_MutatingWebhookConfigurationList proto.InternalMessageInfo

func (m *Rule) Reset()      { *m = Rule{} }
func (*Rule) ProtoMessage() {}
func (*Rule) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{3}
}
func (m *Rule) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Rule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *Rule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rule.Merge(m, src)
}
func (m *Rule) XXX_Size() int {
	return m.Size()
}
func (m *Rule) XXX_DiscardUnknown() {
	xxx_messageInfo_Rule.DiscardUnknown(m)
}

var xxx_messageInfo_Rule proto.InternalMessageInfo

func (m *RuleWithOperations) Reset()      { *m = RuleWithOperations{} }
func (*RuleWithOperations) ProtoMessage() {}
func (*RuleWithOperations) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{4}
}
func (m *RuleWithOperations) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RuleWithOperations) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *RuleWithOperations) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleWithOperations.Merge(m, src)
}
func (m *RuleWithOperations) XXX_Size() int {
	return m.Size()
}
func (m *RuleWithOperations) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleWithOperations.DiscardUnknown(m)
}

var xxx_messageInfo_RuleWithOperations proto.InternalMessageInfo

func (m *ServiceReference) Reset()      { *m = ServiceReference{} }
func (*ServiceReference) ProtoMessage() {}
func (*ServiceReference) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{5}
}
func (m *ServiceReference) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServiceReference) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ServiceReference) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceReference.Merge(m, src)
}
func (m *ServiceReference) XXX_Size() int {
	return m.Size()
}
func (m *ServiceReference) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceReference.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceReference proto.InternalMessageInfo

func (m *ValidatingWebhook) Reset()      { *m = ValidatingWebhook{} }
func (*ValidatingWebhook) ProtoMessage() {}
func (*ValidatingWebhook) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{6}
}
func (m *ValidatingWebhook) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ValidatingWebhook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ValidatingWebhook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidatingWebhook.Merge(m, src)
}
func (m *ValidatingWebhook) XXX_Size() int {
	return m.Size()
}
func (m *ValidatingWebhook) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidatingWebhook.DiscardUnknown(m)
}

var xxx_messageInfo_ValidatingWebhook proto.InternalMessageInfo

func (m *ValidatingWebhookConfiguration) Reset()      { *m = ValidatingWebhookConfiguration{} }
func (*ValidatingWebhookConfiguration) ProtoMessage() {}
func (*ValidatingWebhookConfiguration) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{7}
}
func (m *ValidatingWebhookConfiguration) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ValidatingWebhookConfiguration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ValidatingWebhookConfiguration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidatingWebhookConfiguration.Merge(m, src)
}
func (m *ValidatingWebhookConfiguration) XXX_Size() int {
	return m.Size()
}
func (m *ValidatingWebhookConfiguration) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidatingWebhookConfiguration.DiscardUnknown(m)
}

var xxx_messageInfo_ValidatingWebhookConfiguration proto.InternalMessageInfo

func (m *ValidatingWebhookConfigurationList) Reset()      { *m = ValidatingWebhookConfigurationList{} }
func (*ValidatingWebhookConfigurationList) ProtoMessage() {}
func (*ValidatingWebhookConfigurationList) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{8}
}
func (m *ValidatingWebhookConfigurationList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ValidatingWebhookConfigurationList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ValidatingWebhookConfigurationList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidatingWebhookConfigurationList.Merge(m, src)
}
func (m *ValidatingWebhookConfigurationList) XXX_Size() int {
	return m.Size()
}
func (m *ValidatingWebhookConfigurationList) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidatingWebhookConfigurationList.DiscardUnknown(m)
}

var xxx_messageInfo_ValidatingWebhookConfigurationList proto.InternalMessageInfo

func (m *WebhookClientConfig) Reset()      { *m = WebhookClientConfig{} }
func (*WebhookClientConfig) ProtoMessage() {}
func (*WebhookClientConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_abeea74cbc46f55a, []int{9}
}
func (m *WebhookClientConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WebhookClientConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *WebhookClientConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WebhookClientConfig.Merge(m, src)
}
func (m *WebhookClientConfig) XXX_Size() int {
	return m.Size()
}
func (m *WebhookClientConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_WebhookClientConfig.DiscardUnknown(m)
}

var xxx_messageInfo_WebhookClientConfig proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MutatingWebhook)(nil), "k8s.io.api.admissionregistration.v1beta1.MutatingWebhook")
	proto.RegisterType((*MutatingWebhookConfiguration)(nil), "k8s.io.api.admissionregistration.v1beta1.MutatingWebhookConfiguration")
	proto.RegisterType((*MutatingWebhookConfigurationList)(nil), "k8s.io.api.admissionregistration.v1beta1.MutatingWebhookConfigurationList")
	proto.RegisterType((*Rule)(nil), "k8s.io.api.admissionregistration.v1beta1.Rule")
	proto.RegisterType((*RuleWithOperations)(nil), "k8s.io.api.admissionregistration.v1beta1.RuleWithOperations")
	proto.RegisterType((*ServiceReference)(nil), "k8s.io.api.admissionregistration.v1beta1.ServiceReference")
	proto.RegisterType((*ValidatingWebhook)(nil), "k8s.io.api.admissionregistration.v1beta1.ValidatingWebhook")
	proto.RegisterType((*ValidatingWebhookConfiguration)(nil), "k8s.io.api.admissionregistration.v1beta1.ValidatingWebhookConfiguration")
	proto.RegisterType((*ValidatingWebhookConfigurationList)(nil), "k8s.io.api.admissionregistration.v1beta1.ValidatingWebhookConfigurationList")
	proto.RegisterType((*WebhookClientConfig)(nil), "k8s.io.api.admissionregistration.v1beta1.WebhookClientConfig")
}

func init() {
	proto.RegisterFile("k8s.io/kubernetes/vendor/k8s.io/api/admissionregistration/v1beta1/generated.proto", fileDescriptor_abeea74cbc46f55a)
}

var fileDescriptor_abeea74cbc46f55a = []byte{
	// 1112 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x57, 0x4b, 0x6f, 0x23, 0x45,
	0x10, 0xce, 0xc4, 0xf6, 0xc6, 0x6e, 0xe7, 0xb1, 0x69, 0x60, 0xd7, 0x84, 0x95, 0xc7, 0xf2, 0x01,
	0x59, 0x02, 0x66, 0x36, 0x01, 0x21, 0x58, 0x40, 0x28, 0x0e, 0x2c, 0x44, 0x4a, 0xb2, 0xa1, 0xb3,
	0x0f, 0x89, 0x87, 0xb4, 0xed, 0x71, 0xd9, 0x6e, 0x6c, 0x4f, 0x8f, 0xa6, 0x7b, 0xbc, 0xe4, 0xc6,
	0x4f, 0xe0, 0x2f, 0x70, 0xe2, 0x57, 0x70, 0xe0, 0x16, 0x6e, 0x7b, 0xdc, 0x0b, 0x23, 0x32, 0x9c,
	0x38, 0x70, 0xe0, 0x9a, 0x13, 0xea, 0x9e, 0xf1, 0xf8, 0x95, 0x2c, 0x26, 0x48, 0x7b, 0xca, 0xcd,
	0xfd, 0x55, 0xd7, 0x57, 0x5d, 0x35, 0x55, 0xf5, 0xc9, 0xe8, 0x8b, 0xee, 0x7b, 0xc2, 0x62, 0xdc,
	0xee, 0x06, 0x0d, 0xf0, 0x5d, 0x90, 0x20, 0xec, 0x01, 0xb8, 0x4d, 0xee, 0xdb, 0x89, 0x81, 0x7a,
	0xcc, 0xa6, 0xcd, 0x3e, 0x13, 0x82, 0x71, 0xd7, 0x87, 0x36, 0x13, 0xd2, 0xa7, 0x92, 0x71, 0xd7,
	0x1e, 0x6c, 0x36, 0x40, 0xd2, 0x4d, 0xbb, 0x0d, 0x2e, 0xf8, 0x54, 0x42, 0xd3, 0xf2, 0x7c, 0x2e,
	0x39, 0xae, 0xc5, 0x9e, 0x16, 0xf5, 0x98, 0x75, 0xae, 0xa7, 0x95, 0x78, 0x6e, 0xbc, 0xd5, 0x66,
	0xb2, 0x13, 0x34, 0x2c, 0x87, 0xf7, 0xed, 0x36, 0x6f, 0x73, 0x5b, 0x13, 0x34, 0x82, 0x96, 0x3e,
	0xe9, 0x83, 0xfe, 0x15, 0x13, 0x6f, 0xbc, 0x33, 0x7a, 0x52, 0x9f, 0x3a, 0x1d, 0xe6, 0x82, 0x7f,
	0x6c, 0x7b, 0xdd, 0xb6, 0x02, 0x84, 0xdd, 0x07, 0x49, 0xed, 0xc1, 0xcc, 0x73, 0x36, 0xec, 0x8b,
	0xbc, 0xfc, 0xc0, 0x95, 0xac, 0x0f, 0x33, 0x0e, 0xef, 0xfe, 0x9b, 0x83, 0x70, 0x3a, 0xd0, 0xa7,
	0xd3, 0x7e, 0xd5, 0x5f, 0x97, 0xd0, 0xda, 0x7e, 0x20, 0xa9, 0x64, 0x6e, 0xfb, 0x11, 0x34, 0x3a,
	0x9c, 0x77, 0x71, 0x05, 0x65, 0x5d, 0xda, 0x87, 0x92, 0x51, 0x31, 0x6a, 0x85, 0xfa, 0xf2, 0x49,
	0x68, 0x2e, 0x44, 0xa1, 0x99, 0x3d, 0xa0, 0x7d, 0x20, 0xda, 0x82, 0x9f, 0xa0, 0x65, 0xa7, 0xc7,
	0xc0, 0x95, 0x3b, 0xdc, 0x6d, 0xb1, 0x76, 0x69, 0xb1, 0x62, 0xd4, 0x8a, 0x5b, 0x1f, 0x59, 0xf3,
	0x16, 0xd1, 0x4a, 0x42, 0xed, 0x8c, 0x91, 0xd4, 0x5f, 0x4e, 0x02, 0x2d, 0x8f, 0xa3, 0x64, 0x22,
	0x10, 0xa6, 0x28, 0xe7, 0x07, 0x3d, 0x10, 0xa5, 0x4c, 0x25, 0x53, 0x2b, 0x6e, 0x7d, 0x38, 0x7f,
	0x44, 0x12, 0xf4, 0xe0, 0x11, 0x93, 0x9d, 0x7b, 0x1e, 0xc4, 0x16, 0x51, 0x5f, 0x49, 0x02, 0xe6,
	0x94, 0x4d, 0x90, 0x98, 0x19, 0xef, 0xa1, 0x95, 0x16, 0x65, 0xbd, 0xc0, 0x87, 0x43, 0xde, 0x63,
	0xce, 0x71, 0x29, 0xab, 0xcb, 0xf0, 0x7a, 0x14, 0x9a, 0x2b, 0x77, 0xc7, 0x0d, 0x67, 0xa1, 0xb9,
	0x3e, 0x01, 0xdc, 0x3f, 0xf6, 0x80, 0x4c, 0x3a, 0xe3, 0x4f, 0x50, 0xb1, 0x4f, 0xa5, 0xd3, 0x49,
	0xb8, 0x0a, 0x9a, 0xab, 0x1a, 0x85, 0x66, 0x71, 0x7f, 0x04, 0x9f, 0x85, 0xe6, 0xda, 0xd8, 0x51,
	0xf3, 0x8c, 0xbb, 0xe1, 0xef, 0xd0, 0xba, 0xaa, 0xbb, 0xf0, 0xa8, 0x03, 0x47, 0xd0, 0x03, 0x47,
	0x72, 0xbf, 0x94, 0xd3, 0x45, 0x7f, 0x7b, 0xac, 0x04, 0xe9, 0x97, 0xb7, 0xbc, 0x6e, 0x5b, 0x01,
	0xc2, 0x52, 0x0d, 0x66, 0x0d, 0x36, 0xad, 0x3d, 0xda, 0x80, 0xde, 0xd0, 0xb5, 0xfe, 0x4a, 0x14,
	0x9a, 0xeb, 0x07, 0xd3, 0x8c, 0x64, 0x36, 0x08, 0xe6, 0x68, 0x95, 0x37, 0xbe, 0x05, 0x47, 0xa6,
	0x61, 0x8b, 0x97, 0x0f, 0x8b, 0xa3, 0xd0, 0x5c, 0xbd, 0x37, 0x41, 0x47, 0xa6, 0xe8, 0x55, 0xc1,
	0x04, 0x6b, 0xc2, 0xa7, 0xad, 0x16, 0x38, 0x52, 0x94, 0xae, 0x8d, 0x0a, 0x76, 0x34, 0x82, 0x55,
	0xc1, 0x46, 0xc7, 0x9d, 0x1e, 0x15, 0x82, 0x8c, 0xbb, 0xe1, 0x3b, 0x68, 0x55, 0x75, 0x3d, 0x0f,
	0xe4, 0x11, 0x38, 0xdc, 0x6d, 0x8a, 0xd2, 0x52, 0xc5, 0xa8, 0xe5, 0xe2, 0x17, 0xdc, 0x9f, 0xb0,
	0x90, 0xa9, 0x9b, 0xf8, 0x01, 0xba, 0x99, 0xb6, 0x12, 0x81, 0x01, 0x83, 0x27, 0x0f, 0xc1, 0x57,
	0x07, 0x51, 0xca, 0x57, 0x32, 0xb5, 0x42, 0xfd, 0xb5, 0x28, 0x34, 0x6f, 0x6e, 0x9f, 0x7f, 0x85,
	0x5c, 0xe4, 0x8b, 0x1f, 0x23, 0xec, 0x03, 0x73, 0x07, 0xdc, 0xd1, 0xed, 0x97, 0x34, 0x04, 0xd2,
	0xf9, 0xdd, 0x8e, 0x42, 0x13, 0x93, 0x19, 0xeb, 0x59, 0x68, 0xde, 0x98, 0x45, 0x75, 0x7b, 0x9c,
	0xc3, 0x55, 0xfd, 0xcd, 0x40, 0xb7, 0xa6, 0x66, 0x39, 0x1e, 0x9b, 0x20, 0xee, 0x78, 0xfc, 0x18,
	0xe5, 0xd5, 0x87, 0x69, 0x52, 0x49, 0xf5, 0x70, 0x17, 0xb7, 0x6e, 0xcf, 0xf7, 0x19, 0xe3, 0x6f,
	0xb6, 0x0f, 0x92, 0xd6, 0x71, 0x32, 0x34, 0x68, 0x84, 0x91, 0x94, 0x15, 0x7f, 0x85, 0xf2, 0x49,
	0x64, 0x51, 0x5a, 0xd4, 0x23, 0xfa, 0xfe, 0xfc, 0x23, 0x3a, 0xf5, 0xf6, 0x7a, 0x56, 0x85, 0x22,
	0x29, 0x61, 0xf5, 0x2f, 0x03, 0x55, 0x9e, 0x97, 0xdf, 0x1e, 0x13, 0x12, 0x7f, 0x3d, 0x93, 0xa3,
	0x35, 0x67, 0xab, 0x32, 0x11, 0x67, 0x78, 0x3d, 0xc9, 0x30, 0x3f, 0x44, 0xc6, 0xf2, 0xeb, 0xa2,
	0x1c, 0x93, 0xd0, 0x1f, 0x26, 0x77, 0xf7, 0xd2, 0xc9, 0x4d, 0x3c, 0x7c, 0xb4, 0x89, 0x76, 0x15,
	0x39, 0x89, 0x63, 0x54, 0x7f, 0x31, 0x50, 0x56, 0xad, 0x26, 0xfc, 0x06, 0x2a, 0x50, 0x8f, 0x7d,
	0xe6, 0xf3, 0xc0, 0x13, 0x25, 0x43, 0xf7, 0xe0, 0x4a, 0x14, 0x9a, 0x85, 0xed, 0xc3, 0xdd, 0x18,
	0x24, 0x23, 0x3b, 0xde, 0x44, 0x45, 0xea, 0xb1, 0xb4, 0x65, 0x17, 0xf5, 0xf5, 0x35, 0x35, 0x40,
	0xdb, 0x87, 0xbb, 0x69, 0x9b, 0x8e, 0xdf, 0x51, 0xfc, 0x3e, 0x08, 0x1e, 0xf8, 0x4e, 0xb2, 0x59,
	0x13, 0x7e, 0x32, 0x04, 0xc9, 0xc8, 0x8e, 0xdf, 0x44, 0x39, 0xe1, 0x70, 0x0f, 0x92, 0xbd, 0x78,
	0x43, 0x3d, 0xfb, 0x48, 0x01, 0x67, 0xa1, 0x59, 0xd0, 0x3f, 0x74, 0x83, 0xc6, 0x97, 0xaa, 0x3f,
	0x19, 0x08, 0xcf, 0xae, 0x5e, 0xfc, 0x31, 0x42, 0x3c, 0x3d, 0x25, 0x29, 0x99, 0xba, 0xab, 0x52,
	0xf4, 0x2c, 0x34, 0x57, 0xd2, 0x93, 0xa6, 0x1c, 0x73, 0xc1, 0x87, 0x28, 0xab, 0xd6, 0x75, 0xa2,
	0x3c, 0xd6, 0x7f, 0xd3, 0x81, 0x91, 0xa6, 0xa9, 0x13, 0xd1, 0x4c, 0xd5, 0x1f, 0x0d, 0x74, 0xfd,
	0x08, 0xfc, 0x01, 0x73, 0x80, 0x40, 0x0b, 0x7c, 0x70, 0x1d, 0xc0, 0x36, 0x2a, 0xa4, 0x3b, 0x31,
	0xd1, 0xc3, 0xf5, 0xc4, 0xb7, 0x90, 0xee, 0x4f, 0x32, 0xba, 0x93, 0x6a, 0xe7, 0xe2, 0x85, 0xda,
	0x79, 0x0b, 0x65, 0x3d, 0x2a, 0x3b, 0xa5, 0x8c, 0xbe, 0x91, 0x57, 0xd6, 0x43, 0x2a, 0x3b, 0x44,
	0xa3, 0xda, 0xca, 0x7d, 0xa9, 0x8b, 0x9b, 0x4b, 0xac, 0xdc, 0x97, 0x44, 0xa3, 0xd5, 0x3f, 0xaf,
	0xa1, 0xf5, 0x87, 0xb4, 0xc7, 0x9a, 0x57, 0x7a, 0x7d, 0xa5, 0xd7, 0x73, 0xea, 0x35, 0xba, 0xd2,
	0xeb, 0xcb, 0xe8, 0x75, 0xf5, 0xd4, 0x40, 0xe5, 0x99, 0x59, 0x7b, 0xd1, 0x7a, 0xfa, 0xcd, 0x8c,
	0x9e, 0x7e, 0x30, 0xff, 0x08, 0xcd, 0xbc, 0x7e, 0x46, 0x51, 0xff, 0x36, 0x50, 0xf5, 0xf9, 0x39,
	0xbe, 0x00, 0x4d, 0xed, 0x4f, 0x6a, 0xea, 0xe7, 0xff, 0x23, 0xc1, 0x79, 0x54, 0xf5, 0x67, 0x03,
	0xbd, 0x74, 0xce, 0x3a, 0xc3, 0xaf, 0xa2, 0x4c, 0xe0, 0xf7, 0x92, 0xb5, 0xbc, 0x14, 0x85, 0x66,
	0xe6, 0x01, 0xd9, 0x23, 0x0a, 0xc3, 0x14, 0x2d, 0x89, 0x58, 0x19, 0x92, 0xf4, 0xef, 0xcc, 0xff,
	0xc6, 0x69, 0x49, 0xa9, 0x17, 0xa3, 0xd0, 0x5c, 0x1a, 0xa2, 0x43, 0x5e, 0x5c, 0x43, 0x79, 0x87,
	0xd6, 0x03, 0xb7, 0x99, 0x68, 0xda, 0x72, 0x7d, 0x59, 0x95, 0x6b, 0x67, 0x3b, 0xc6, 0x48, 0x6a,
	0xad, 0x1f, 0x9c, 0x9c, 0x96, 0x17, 0x9e, 0x9e, 0x96, 0x17, 0x9e, 0x9d, 0x96, 0x17, 0xbe, 0x8f,
	0xca, 0xc6, 0x49, 0x54, 0x36, 0x9e, 0x46, 0x65, 0xe3, 0x59, 0x54, 0x36, 0x7e, 0x8f, 0xca, 0xc6,
	0x0f, 0x7f, 0x94, 0x17, 0xbe, 0xac, 0xcd, 0xfb, 0x4f, 0xf8, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x29, 0xe6, 0x3d, 0x13, 0x4d, 0x0f, 0x00, 0x00,
}

func (m *MutatingWebhook) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutatingWebhook) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MutatingWebhook) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ObjectSelector != nil {
		{
			size, err := m.ObjectSelector.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.ReinvocationPolicy != nil {
		i -= len(*m.ReinvocationPolicy)
		copy(dAtA[i:], *m.ReinvocationPolicy)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.ReinvocationPolicy)))
		i--
		dAtA[i] = 0x52
	}
	if m.MatchPolicy != nil {
		i -= len(*m.MatchPolicy)
		copy(dAtA[i:], *m.MatchPolicy)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.MatchPolicy)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.AdmissionReviewVersions) > 0 {
		for iNdEx := len(m.AdmissionReviewVersions) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.AdmissionReviewVersions[iNdEx])
			copy(dAtA[i:], m.AdmissionReviewVersions[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.AdmissionReviewVersions[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if m.TimeoutSeconds != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.TimeoutSeconds))
		i--
		dAtA[i] = 0x38
	}
	if m.SideEffects != nil {
		i -= len(*m.SideEffects)
		copy(dAtA[i:], *m.SideEffects)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.SideEffects)))
		i--
		dAtA[i] = 0x32
	}
	if m.NamespaceSelector != nil {
		{
			size, err := m.NamespaceSelector.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.FailurePolicy != nil {
		i -= len(*m.FailurePolicy)
		copy(dAtA[i:], *m.FailurePolicy)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.FailurePolicy)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Rules) > 0 {
		for iNdEx := len(m.Rules) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rules[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	{
		size, err := m.ClientConfig.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *MutatingWebhookConfiguration) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutatingWebhookConfiguration) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MutatingWebhookConfiguration) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Webhooks) > 0 {
		for iNdEx := len(m.Webhooks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Webhooks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *MutatingWebhookConfigurationList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutatingWebhookConfigurationList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MutatingWebhookConfigurationList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Rule) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Rule) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Rule) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Scope != nil {
		i -= len(*m.Scope)
		copy(dAtA[i:], *m.Scope)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Scope)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Resources) > 0 {
		for iNdEx := len(m.Resources) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Resources[iNdEx])
			copy(dAtA[i:], m.Resources[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Resources[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.APIVersions) > 0 {
		for iNdEx := len(m.APIVersions) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.APIVersions[iNdEx])
			copy(dAtA[i:], m.APIVersions[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.APIVersions[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.APIGroups) > 0 {
		for iNdEx := len(m.APIGroups) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.APIGroups[iNdEx])
			copy(dAtA[i:], m.APIGroups[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.APIGroups[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *RuleWithOperations) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RuleWithOperations) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RuleWithOperations) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Rule.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Operations) > 0 {
		for iNdEx := len(m.Operations) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Operations[iNdEx])
			copy(dAtA[i:], m.Operations[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Operations[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ServiceReference) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServiceReference) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServiceReference) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Port != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.Port))
		i--
		dAtA[i] = 0x20
	}
	if m.Path != nil {
		i -= len(*m.Path)
		copy(dAtA[i:], *m.Path)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Path)))
		i--
		dAtA[i] = 0x1a
	}
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Namespace)
	copy(dAtA[i:], m.Namespace)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Namespace)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ValidatingWebhook) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidatingWebhook) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ValidatingWebhook) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ObjectSelector != nil {
		{
			size, err := m.ObjectSelector.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.MatchPolicy != nil {
		i -= len(*m.MatchPolicy)
		copy(dAtA[i:], *m.MatchPolicy)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.MatchPolicy)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.AdmissionReviewVersions) > 0 {
		for iNdEx := len(m.AdmissionReviewVersions) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.AdmissionReviewVersions[iNdEx])
			copy(dAtA[i:], m.AdmissionReviewVersions[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.AdmissionReviewVersions[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if m.TimeoutSeconds != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.TimeoutSeconds))
		i--
		dAtA[i] = 0x38
	}
	if m.SideEffects != nil {
		i -= len(*m.SideEffects)
		copy(dAtA[i:], *m.SideEffects)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.SideEffects)))
		i--
		dAtA[i] = 0x32
	}
	if m.NamespaceSelector != nil {
		{
			size, err := m.NamespaceSelector.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.FailurePolicy != nil {
		i -= len(*m.FailurePolicy)
		copy(dAtA[i:], *m.FailurePolicy)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.FailurePolicy)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Rules) > 0 {
		for iNdEx := len(m.Rules) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rules[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	{
		size, err := m.ClientConfig.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ValidatingWebhookConfiguration) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidatingWebhookConfiguration) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ValidatingWebhookConfiguration) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Webhooks) > 0 {
		for iNdEx := len(m.Webhooks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Webhooks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ValidatingWebhookConfigurationList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidatingWebhookConfigurationList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ValidatingWebhookConfigurationList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *WebhookClientConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WebhookClientConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WebhookClientConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.URL != nil {
		i -= len(*m.URL)
		copy(dAtA[i:], *m.URL)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.URL)))
		i--
		dAtA[i] = 0x1a
	}
	if m.CABundle != nil {
		i -= len(m.CABundle)
		copy(dAtA[i:], m.CABundle)
		i = encodeVarintGenerated(dAtA, i, uint64(len(m.CABundle)))
		i--
		dAtA[i] = 0x12
	}
	if m.Service != nil {
		{
			size, err := m.Service.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintGenerated(dAtA []byte, offset int, v uint64) int {
	offset -= sovGenerated(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *MutatingWebhook) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	l = m.ClientConfig.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Rules) > 0 {
		for _, e := range m.Rules {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.FailurePolicy != nil {
		l = len(*m.FailurePolicy)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.NamespaceSelector != nil {
		l = m.NamespaceSelector.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.SideEffects != nil {
		l = len(*m.SideEffects)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.TimeoutSeconds != nil {
		n += 1 + sovGenerated(uint64(*m.TimeoutSeconds))
	}
	if len(m.AdmissionReviewVersions) > 0 {
		for _, s := range m.AdmissionReviewVersions {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.MatchPolicy != nil {
		l = len(*m.MatchPolicy)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.ReinvocationPolicy != nil {
		l = len(*m.ReinvocationPolicy)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.ObjectSelector != nil {
		l = m.ObjectSelector.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *MutatingWebhookConfiguration) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Webhooks) > 0 {
		for _, e := range m.Webhooks {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *MutatingWebhookConfigurationList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *Rule) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.APIGroups) > 0 {
		for _, s := range m.APIGroups {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.APIVersions) > 0 {
		for _, s := range m.APIVersions {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.Resources) > 0 {
		for _, s := range m.Resources {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.Scope != nil {
		l = len(*m.Scope)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *RuleWithOperations) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Operations) > 0 {
		for _, s := range m.Operations {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	l = m.Rule.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *ServiceReference) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Namespace)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	if m.Path != nil {
		l = len(*m.Path)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Port != nil {
		n += 1 + sovGenerated(uint64(*m.Port))
	}
	return n
}

func (m *ValidatingWebhook) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	l = m.ClientConfig.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Rules) > 0 {
		for _, e := range m.Rules {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.FailurePolicy != nil {
		l = len(*m.FailurePolicy)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.NamespaceSelector != nil {
		l = m.NamespaceSelector.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.SideEffects != nil {
		l = len(*m.SideEffects)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.TimeoutSeconds != nil {
		n += 1 + sovGenerated(uint64(*m.TimeoutSeconds))
	}
	if len(m.AdmissionReviewVersions) > 0 {
		for _, s := range m.AdmissionReviewVersions {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.MatchPolicy != nil {
		l = len(*m.MatchPolicy)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.ObjectSelector != nil {
		l = m.ObjectSelector.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *ValidatingWebhookConfiguration) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Webhooks) > 0 {
		for _, e := range m.Webhooks {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *ValidatingWebhookConfigurationList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *WebhookClientConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Service != nil {
		l = m.Service.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.CABundle != nil {
		l = len(m.CABundle)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.URL != nil {
		l = len(*m.URL)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func sovGenerated(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGenerated(x uint64) (n int) {
	return sovGenerated(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *MutatingWebhook) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRules := "[]RuleWithOperations{"
	for _, f := range this.Rules {
		repeatedStringForRules += strings.Replace(strings.Replace(f.String(), "RuleWithOperations", "RuleWithOperations", 1), `&`, ``, 1) + ","
	}
	repeatedStringForRules += "}"
	s := strings.Join([]string{`&MutatingWebhook{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`ClientConfig:` + strings.Replace(strings.Replace(this.ClientConfig.String(), "WebhookClientConfig", "WebhookClientConfig", 1), `&`, ``, 1) + `,`,
		`Rules:` + repeatedStringForRules + `,`,
		`FailurePolicy:` + valueToStringGenerated(this.FailurePolicy) + `,`,
		`NamespaceSelector:` + strings.Replace(fmt.Sprintf("%v", this.NamespaceSelector), "LabelSelector", "v1.LabelSelector", 1) + `,`,
		`SideEffects:` + valueToStringGenerated(this.SideEffects) + `,`,
		`TimeoutSeconds:` + valueToStringGenerated(this.TimeoutSeconds) + `,`,
		`AdmissionReviewVersions:` + fmt.Sprintf("%v", this.AdmissionReviewVersions) + `,`,
		`MatchPolicy:` + valueToStringGenerated(this.MatchPolicy) + `,`,
		`ReinvocationPolicy:` + valueToStringGenerated(this.ReinvocationPolicy) + `,`,
		`ObjectSelector:` + strings.Replace(fmt.Sprintf("%v", this.ObjectSelector), "LabelSelector", "v1.LabelSelector", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *MutatingWebhookConfiguration) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForWebhooks := "[]MutatingWebhook{"
	for _, f := range this.Webhooks {
		repeatedStringForWebhooks += strings.Replace(strings.Replace(f.String(), "MutatingWebhook", "MutatingWebhook", 1), `&`, ``, 1) + ","
	}
	repeatedStringForWebhooks += "}"
	s := strings.Join([]string{`&MutatingWebhookConfiguration{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Webhooks:` + repeatedStringForWebhooks + `,`,
		`}`,
	}, "")
	return s
}
func (this *MutatingWebhookConfigurationList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]MutatingWebhookConfiguration{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "MutatingWebhookConfiguration", "MutatingWebhookConfiguration", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&MutatingWebhookConfigurationList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *Rule) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Rule{`,
		`APIGroups:` + fmt.Sprintf("%v", this.APIGroups) + `,`,
		`APIVersions:` + fmt.Sprintf("%v", this.APIVersions) + `,`,
		`Resources:` + fmt.Sprintf("%v", this.Resources) + `,`,
		`Scope:` + valueToStringGenerated(this.Scope) + `,`,
		`}`,
	}, "")
	return s
}
func (this *RuleWithOperations) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&RuleWithOperations{`,
		`Operations:` + fmt.Sprintf("%v", this.Operations) + `,`,
		`Rule:` + strings.Replace(strings.Replace(this.Rule.String(), "Rule", "Rule", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ServiceReference) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ServiceReference{`,
		`Namespace:` + fmt.Sprintf("%v", this.Namespace) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Path:` + valueToStringGenerated(this.Path) + `,`,
		`Port:` + valueToStringGenerated(this.Port) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ValidatingWebhook) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRules := "[]RuleWithOperations{"
	for _, f := range this.Rules {
		repeatedStringForRules += strings.Replace(strings.Replace(f.String(), "RuleWithOperations", "RuleWithOperations", 1), `&`, ``, 1) + ","
	}
	repeatedStringForRules += "}"
	s := strings.Join([]string{`&ValidatingWebhook{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`ClientConfig:` + strings.Replace(strings.Replace(this.ClientConfig.String(), "WebhookClientConfig", "WebhookClientConfig", 1), `&`, ``, 1) + `,`,
		`Rules:` + repeatedStringForRules + `,`,
		`FailurePolicy:` + valueToStringGenerated(this.FailurePolicy) + `,`,
		`NamespaceSelector:` + strings.Replace(fmt.Sprintf("%v", this.NamespaceSelector), "LabelSelector", "v1.LabelSelector", 1) + `,`,
		`SideEffects:` + valueToStringGenerated(this.SideEffects) + `,`,
		`TimeoutSeconds:` + valueToStringGenerated(this.TimeoutSeconds) + `,`,
		`AdmissionReviewVersions:` + fmt.Sprintf("%v", this.AdmissionReviewVersions) + `,`,
		`MatchPolicy:` + valueToStringGenerated(this.MatchPolicy) + `,`,
		`ObjectSelector:` + strings.Replace(fmt.Sprintf("%v", this.ObjectSelector), "LabelSelector", "v1.LabelSelector", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ValidatingWebhookConfiguration) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForWebhooks := "[]ValidatingWebhook{"
	for _, f := range this.Webhooks {
		repeatedStringForWebhooks += strings.Replace(strings.Replace(f.String(), "ValidatingWebhook", "ValidatingWebhook", 1), `&`, ``, 1) + ","
	}
	repeatedStringForWebhooks += "}"
	s := strings.Join([]string{`&ValidatingWebhookConfiguration{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Webhooks:` + repeatedStringForWebhooks + `,`,
		`}`,
	}, "")
	return s
}
func (this *ValidatingWebhookConfigurationList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]ValidatingWebhookConfiguration{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "ValidatingWebhookConfiguration", "ValidatingWebhookConfiguration", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&ValidatingWebhookConfigurationList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *WebhookClientConfig) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&WebhookClientConfig{`,
		`Service:` + strings.Replace(this.Service.String(), "ServiceReference", "ServiceReference", 1) + `,`,
		`CABundle:` + valueToStringGenerated(this.CABundle) + `,`,
		`URL:` + valueToStringGenerated(this.URL) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGenerated(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *MutatingWebhook) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MutatingWebhook: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MutatingWebhook: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ClientConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rules = append(m.Rules, RuleWithOperations{})
			if err := m.Rules[len(m.Rules)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FailurePolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := FailurePolicyType(dAtA[iNdEx:postIndex])
			m.FailurePolicy = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NamespaceSelector", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NamespaceSelector == nil {
				m.NamespaceSelector = &v1.LabelSelector{}
			}
			if err := m.NamespaceSelector.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SideEffects", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := SideEffectClass(dAtA[iNdEx:postIndex])
			m.SideEffects = &s
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeoutSeconds", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TimeoutSeconds = &v
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdmissionReviewVersions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdmissionReviewVersions = append(m.AdmissionReviewVersions, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MatchPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := MatchPolicyType(dAtA[iNdEx:postIndex])
			m.MatchPolicy = &s
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReinvocationPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := ReinvocationPolicyType(dAtA[iNdEx:postIndex])
			m.ReinvocationPolicy = &s
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectSelector", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ObjectSelector == nil {
				m.ObjectSelector = &v1.LabelSelector{}
			}
			if err := m.ObjectSelector.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MutatingWebhookConfiguration) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MutatingWebhookConfiguration: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MutatingWebhookConfiguration: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Webhooks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Webhooks = append(m.Webhooks, MutatingWebhook{})
			if err := m.Webhooks[len(m.Webhooks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MutatingWebhookConfigurationList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MutatingWebhookConfigurationList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MutatingWebhookConfigurationList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, MutatingWebhookConfiguration{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Rule) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Rule: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Rule: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field APIGroups", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.APIGroups = append(m.APIGroups, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field APIVersions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.APIVersions = append(m.APIVersions, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Resources", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Resources = append(m.Resources, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Scope", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := ScopeType(dAtA[iNdEx:postIndex])
			m.Scope = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RuleWithOperations) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RuleWithOperations: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RuleWithOperations: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operations", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Operations = append(m.Operations, OperationType(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Rule.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServiceReference) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServiceReference: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServiceReference: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Namespace", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Namespace = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Path = &s
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Port = &v
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidatingWebhook) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ValidatingWebhook: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ValidatingWebhook: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ClientConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rules = append(m.Rules, RuleWithOperations{})
			if err := m.Rules[len(m.Rules)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FailurePolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := FailurePolicyType(dAtA[iNdEx:postIndex])
			m.FailurePolicy = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NamespaceSelector", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NamespaceSelector == nil {
				m.NamespaceSelector = &v1.LabelSelector{}
			}
			if err := m.NamespaceSelector.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SideEffects", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := SideEffectClass(dAtA[iNdEx:postIndex])
			m.SideEffects = &s
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeoutSeconds", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TimeoutSeconds = &v
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdmissionReviewVersions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdmissionReviewVersions = append(m.AdmissionReviewVersions, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MatchPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := MatchPolicyType(dAtA[iNdEx:postIndex])
			m.MatchPolicy = &s
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectSelector", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ObjectSelector == nil {
				m.ObjectSelector = &v1.LabelSelector{}
			}
			if err := m.ObjectSelector.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidatingWebhookConfiguration) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ValidatingWebhookConfiguration: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ValidatingWebhookConfiguration: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Webhooks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Webhooks = append(m.Webhooks, ValidatingWebhook{})
			if err := m.Webhooks[len(m.Webhooks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidatingWebhookConfigurationList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ValidatingWebhookConfigurationList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ValidatingWebhookConfigurationList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, ValidatingWebhookConfiguration{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WebhookClientConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WebhookClientConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WebhookClientConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Service", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Service == nil {
				m.Service = &ServiceReference{}
			}
			if err := m.Service.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CABundle", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CABundle = append(m.CABundle[:0], dAtA[iNdEx:postIndex]...)
			if m.CABundle == nil {
				m.CABundle = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field URL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.URL = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGenerated(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGenerated
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupGenerated
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthGenerated
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthGenerated        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGenerated          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupGenerated = fmt.Errorf("proto: unexpected end of group")
)
