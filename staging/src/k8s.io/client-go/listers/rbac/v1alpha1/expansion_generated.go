/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

// ClusterRoleListerExpansion allows custom methods to be added to
// ClusterRoleLister.
type ClusterRoleListerExpansion interface{}

// ClusterRoleBindingListerExpansion allows custom methods to be added to
// ClusterRoleBindingLister.
type ClusterRoleBindingListerExpansion interface{}

// RoleListerExpansion allows custom methods to be added to
// RoleLister.
type RoleListerExpansion interface{}

// RoleNamespaceListerExpansion allows custom methods to be added to
// RoleNamespaceLister.
type RoleNamespaceListerExpansion interface{}

// RoleBindingListerExpansion allows custom methods to be added to
// RoleBindingLister.
type RoleBindingListerExpansion interface{}

// RoleBindingNamespaceListerExpansion allows custom methods to be added to
// RoleBindingNamespaceLister.
type RoleBindingNamespaceListerExpansion interface{}
