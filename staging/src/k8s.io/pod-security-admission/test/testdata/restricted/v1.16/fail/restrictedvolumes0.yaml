apiVersion: v1
kind: Pod
metadata:
  name: restrictedvolumes0
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
  volumes:
  - gcePersistentDisk:
      pdName: test
    name: volume1
