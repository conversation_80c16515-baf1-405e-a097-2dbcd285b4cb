apiVersion: v1
kind: Pod
metadata:
  name: hostpathvolumes0
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
  volumes:
  - emptyDir: {}
    name: volume-emptydir
  - hostPath:
      path: /a
    name: volume-hostpath
