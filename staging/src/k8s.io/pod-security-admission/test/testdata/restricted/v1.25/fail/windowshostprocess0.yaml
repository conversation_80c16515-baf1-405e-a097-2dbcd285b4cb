apiVersion: v1
kind: Pod
metadata:
  name: windowshostprocess0
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      windowsOptions: {}
  hostNetwork: true
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      windowsOptions: {}
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    windowsOptions:
      hostProcess: true
