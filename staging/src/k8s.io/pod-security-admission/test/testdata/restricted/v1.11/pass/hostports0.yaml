apiVersion: v1
kind: Pod
metadata:
  name: hostports0
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    ports:
    - containerPort: 12345
    securityContext:
      allowPrivilegeEscalation: false
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    ports:
    - containerPort: 12346
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
