apiVersion: v1
kind: Pod
metadata:
  name: privileged0
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      privileged: false
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      privileged: false
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
