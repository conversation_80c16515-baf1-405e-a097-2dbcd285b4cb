apiVersion: v1
kind: Pod
metadata:
  name: windowshostprocess1
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      windowsOptions:
        hostProcess: true
  hostNetwork: true
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      windowsOptions:
        hostProcess: true
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    windowsOptions: {}
