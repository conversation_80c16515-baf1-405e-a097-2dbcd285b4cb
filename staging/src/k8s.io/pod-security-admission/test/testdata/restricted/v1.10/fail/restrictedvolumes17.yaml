apiVersion: v1
kind: Pod
metadata:
  name: restrictedvolumes17
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
  volumes:
  - name: volume1
    scaleIO:
      gateway: localhost
      secretRef: null
      system: test
      volumeName: test
