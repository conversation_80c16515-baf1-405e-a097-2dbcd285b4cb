apiVersion: v1
kind: Pod
metadata:
  name: hostnamespaces1
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  hostNetwork: true
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
