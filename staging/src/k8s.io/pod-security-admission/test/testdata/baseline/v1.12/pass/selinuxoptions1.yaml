apiVersion: v1
kind: Pod
metadata:
  name: selinuxoptions1
spec:
  containers:
  - image: k8s.gcr.io/pause
    name: container1
    securityContext:
      seLinuxOptions:
        level: somevalue
        type: container_init_t
  initContainers:
  - image: k8s.gcr.io/pause
    name: initcontainer1
    securityContext:
      seLinuxOptions:
        type: container_kvm_t
  securityContext:
    seLinuxOptions:
      type: container_t
