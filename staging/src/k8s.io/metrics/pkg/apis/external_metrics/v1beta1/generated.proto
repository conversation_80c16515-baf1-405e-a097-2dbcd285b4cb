/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.metrics.pkg.apis.external_metrics.v1beta1;

import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/metrics/pkg/apis/external_metrics/v1beta1";

// ExternalMetricValue is a metric value for external metric
// A single metric value is identified by metric name and a set of string labels.
// For one metric there can be multiple values with different sets of labels.
message ExternalMetricValue {
  // the name of the metric
  optional string metricName = 1;

  // a set of labels that identify a single time series for the metric
  map<string, string> metricLabels = 2;

  // indicates the time at which the metrics were produced
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time timestamp = 3;

  // indicates the window ([Timestamp-Window, Timestamp]) from
  // which these metrics were calculated, when returning rate
  // metrics calculated from cumulative metrics (or zero for
  // non-calculated instantaneous metrics).
  optional int64 window = 4;

  // the value of the metric
  optional k8s.io.apimachinery.pkg.api.resource.Quantity value = 5;
}

// ExternalMetricValueList is a list of values for a given metric for some set labels
message ExternalMetricValueList {
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // value of the metric matching a given set of labels
  repeated ExternalMetricValue items = 2;
}

