description: create list with errors
mode: create
filename: "svc.yaml"
namespace: "edit-test"
expectedStdout:
- "service/svc1 created"
expectedStderr:
- "\"svc2\" is invalid"
expectedExitCode: 1
steps:
- type: edit
  expectedInput: 0.original
  resultingOutput: 0.edited
- type: request
  expectedMethod: POST
  expectedPath: /api/v1/namespaces/edit-test/services
  expectedContentType: application/json
  expectedInput: 1.request
  resultingStatusCode: 201
  resultingOutput: 1.response
- type: edit
  expectedInput: 2.original
  resultingOutput: 2.edited
- type: request
  expectedMethod: POST
  expectedPath: /api/v1/namespaces/edit-test/services
  expectedContentType: application/json
  expectedInput: 3.request
  resultingStatusCode: 422
  resultingOutput: 3.response
