{"kind": "Service", "apiVersion": "v1", "metadata": {"annotations": {"kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"v1\",\"kind\":\"Service\",\"metadata\":{\"annotations\":{},\"creationTimestamp\":\"2017-02-27T19:40:53Z\",\"labels\":{\"app\":\"svc1\",\"new-label\":\"new-value\"},\"name\":\"svc1\",\"namespace\":\"edit-test\",\"resourceVersion\":\"670\",\"selfLink\":\"/api/v1/namespaces/edit-test/services/svc1\",\"uid\":\"a6c11186-fd24-11e6-b53c-480fcf4a5275\"},\"spec\":{\"clusterIP\":\"**********\",\"ports\":[{\"name\":\"80\",\"port\":80,\"protocol\":\"TCP\",\"targetPort\":80}],\"selector\":{\"app\":\"svc1\"},\"sessionAffinity\":\"None\",\"type\":\"ClusterIP\"},\"status\":{\"loadBalancer\":{}}}\n"}, "name": "svc1", "namespace": "edit-test", "selfLink": "/api/v1/namespaces/edit-test/services/svc1", "uid": "a6c11186-fd24-11e6-b53c-480fcf4a5275", "resourceVersion": "1045", "creationTimestamp": "2017-02-27T19:40:53Z", "labels": {"app": "svc1", "new-label": "new-value"}}, "spec": {"clusterIP": "**********", "ports": [{"name": "80", "port": 80, "protocol": "TCP", "targetPort": 80}], "selector": {"app": "svc1"}, "sessionAffinity": "None", "type": "ClusterIP"}, "status": {"loadBalancer": {}}}