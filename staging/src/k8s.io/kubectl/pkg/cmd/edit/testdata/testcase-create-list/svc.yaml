apiVersion: v1
items:
- apiVersion: v1
  kind: Service
  metadata:
    labels:
      app: svc1
    name: svc1
  spec:
    ports:
    - name: "81"
      port: 81
      protocol: TCP
      targetPort: 81
    selector:
      app: svc1
    sessionAffinity: None
    type: ClusterIP
- apiVersion: v1
  kind: Service
  metadata:
    labels:
      app: svc2
    name: svc2
    namespace: edit-test
  spec:
    ports:
    - name: "80"
      port: 80
      protocol: TCP
      targetPort: 80
    selector:
      app: svc2
    sessionAffinity: None
    type: ClusterIP
kind: List
metadata: {}
resourceVersion: ""
selfLink: ""
